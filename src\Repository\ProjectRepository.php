<?php

namespace App\Repository;

use App\Entity\Project;
use Doctrine\Bundle\DoctrineBundle\Repository\ServiceEntityRepository;
use Doctrine\Persistence\ManagerRegistry;

/**
 * @extends ServiceEntityRepository<Project>
 */
class ProjectRepository extends ServiceEntityRepository
{
    public function __construct(ManagerRegistry $registry)
    {
        parent::__construct($registry, Project::class);
    }

    /**
     * Find projects by user
     */
    public function findByUser($user): array
    {
        return $this->createQueryBuilder('p')
            ->andWhere('p.user = :user')
            ->setParameter('user', $user)
            ->orderBy('p.createdAt', 'DESC')
            ->getQuery()
            ->getResult();
    }

    /**
     * Find projects with their loans and charges
     */
    public function findWithRelations($id)
    {
        return $this->createQueryBuilder('p')
            ->leftJoin('p.loans', 'l')
            ->leftJoin('p.charges', 'c')
            ->addSelect('l', 'c')
            ->andWhere('p.id = :id')
            ->setParameter('id', $id)
            ->getQuery()
            ->getOneOrNullResult();
    }

    /**
     * Find projects with statistics
     */
    public function findWithStatistics($user): array
    {
        return $this->createQueryBuilder('p')
            ->select('p', 'COUNT(l.id) as loanCount', 'COUNT(c.id) as chargeCount')
            ->leftJoin('p.loans', 'l')
            ->leftJoin('p.charges', 'c')
            ->andWhere('p.user = :user')
            ->setParameter('user', $user)
            ->groupBy('p.id')
            ->orderBy('p.createdAt', 'DESC')
            ->getQuery()
            ->getResult();
    }

    /**
     * Search projects by name
     */
    public function searchByName($user, string $searchTerm): array
    {
        return $this->createQueryBuilder('p')
            ->andWhere('p.user = :user')
            ->andWhere('p.name LIKE :searchTerm')
            ->setParameter('user', $user)
            ->setParameter('searchTerm', '%' . $searchTerm . '%')
            ->orderBy('p.createdAt', 'DESC')
            ->getQuery()
            ->getResult();
    }
}
