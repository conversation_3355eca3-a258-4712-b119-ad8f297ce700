<?php

namespace App\Controller;

use App\Entity\Project;
use App\Entity\Loan;
use App\Entity\Charge;
use App\Repository\ProjectRepository;
use App\Service\LoanCalculatorService;
use App\Service\ExpenseCalculatorService;
use App\Service\ProfitabilityCalculatorService;
use Doctrine\ORM\EntityManagerInterface;
use Symfony\Bundle\FrameworkBundle\Controller\AbstractController;
use Symfony\Component\HttpFoundation\Request;
use Symfony\Component\HttpFoundation\Response;
use Symfony\Component\HttpFoundation\JsonResponse;
use Symfony\Component\Routing\Attribute\Route;
use Symfony\Component\Security\Http\Attribute\IsGranted;

#[Route('/projet')]
#[IsGranted('ROLE_USER')]
class ProjectController extends AbstractController
{
    public function __construct(
        private EntityManagerInterface $entityManager,
        private ProjectRepository $projectRepository,
        private LoanCalculatorService $loanCalculator,
        private ExpenseCalculatorService $expenseCalculator,
        private ProfitabilityCalculatorService $profitabilityCalculator
    ) {}

    #[Route('/', name: 'app_project_index', methods: ['GET'])]
    public function index(): Response
    {
        $projects = $this->projectRepository->findBy(['user' => $this->getUser()]);

        return $this->render('project/index.html.twig', [
            'projects' => $projects,
        ]);
    }

    #[Route('/nouveau', name: 'app_project_new', methods: ['GET', 'POST'])]
    public function new(Request $request): Response
    {
        if ($request->isMethod('POST')) {
            $name = $request->request->get('name');
            
            if (empty($name)) {
                $this->addFlash('error', 'Le nom du projet est requis.');
                return $this->redirectToRoute('app_project_index');
            }

            $project = new Project();
            $project->setName($name);
            $project->setUser($this->getUser());
            $project->setIsSci((bool)$request->request->get('isSci', false));

            $this->entityManager->persist($project);
            $this->entityManager->flush();

            $this->addFlash('success', 'Projet créé avec succès !');
            return $this->redirectToRoute('app_project_show', ['id' => $project->getId()]);
        }

        return $this->render('project/new.html.twig');
    }

    #[Route('/{id}', name: 'app_project_show', methods: ['GET'])]
    public function show(Project $project): Response
    {
        $this->denyAccessUnlessGranted('view', $project);

        $initialCharges = [];
        $recurringCharges = [];
        
        foreach ($project->getCharges() as $charge) {
            if ($charge->getType() === Charge::TYPE_INITIAL) {
                $initialCharges[] = $charge;
            } else {
                $recurringCharges[] = $charge;
            }
        }

        // Calculate loan statistics
        $loansStats = null;
        if ($project->getLoans()->count() > 0) {
            $loansStats = $this->loanCalculator->calculateMultipleLoansStatistics($project->getLoans()->toArray());
        }

        // Calculate expense statistics
        $expenseStats = $this->expenseCalculator->getExpenseStatistics($project);

        // Calculate profitability analysis
        $profitabilityAnalysis = $this->profitabilityCalculator->calculateProfitabilityAnalysis($project);

        return $this->render('project/show.html.twig', [
            'project' => $project,
            'loans' => $project->getLoans(),
            'initialCharges' => $initialCharges,
            'recurringCharges' => $recurringCharges,
            'loansStats' => $loansStats,
            'expenseStats' => $expenseStats,
            'profitabilityAnalysis' => $profitabilityAnalysis,
        ]);
    }

    #[Route('/{id}/edit', name: 'app_project_edit', methods: ['POST'])]
    public function edit(Request $request, Project $project): JsonResponse
    {
        $this->denyAccessUnlessGranted('edit', $project);

        $field = $request->request->get('field');
        $value = $request->request->get('value');

        try {
            switch ($field) {
                case 'name':
                    $project->setName($value);
                    break;
                case 'rentAmount':
                    $project->setRentAmount($value ?: null);
                    break;
                case 'vacancyRate':
                    $project->setVacancyRate($value ?: null);
                    break;
                case 'landPrice':
                    $project->setLandPrice($value ?: null);
                    break;
                case 'propertyValue':
                    $project->setPropertyValue($value ?: null);
                    break;
                case 'amortizationDuration':
                    $project->setAmortizationDuration($value ? (int)$value : null);
                    break;
                case 'isSci':
                    $project->setIsSci((bool)$value);
                    break;
                default:
                    return new JsonResponse(['success' => false, 'message' => 'Champ non reconnu']);
            }

            $this->entityManager->flush();

            return new JsonResponse([
                'success' => true,
                'message' => 'Projet mis à jour avec succès',
                'value' => $value
            ]);
        } catch (\Exception $e) {
            return new JsonResponse([
                'success' => false,
                'message' => 'Erreur lors de la mise à jour: ' . $e->getMessage()
            ]);
        }
    }

    #[Route('/{id}/loan/add', name: 'app_project_loan_add', methods: ['POST'])]
    public function addLoan(Request $request, Project $project): JsonResponse
    {
        $this->denyAccessUnlessGranted('edit', $project);

        try {
            $loan = new Loan();
            $loan->setProject($project);
            $loan->setAmount($request->request->get('amount'));
            $loan->setInterestRate($request->request->get('interestRate'));
            $loan->setInsuranceRate($request->request->get('insuranceRate'));
            $loan->setDurationYears((int)$request->request->get('durationYears'));
            $loan->setStartDate(new \DateTime($request->request->get('startDate')));
            $loan->setDeferredMonths((int)$request->request->get('deferredMonths', 0));

            $this->entityManager->persist($loan);
            $this->entityManager->flush();

            $loanStats = $this->loanCalculator->calculateLoanStatistics($loan);

            return new JsonResponse([
                'success' => true,
                'message' => 'Prêt ajouté avec succès',
                'loan' => [
                    'id' => $loan->getId(),
                    'amount' => $loan->getAmount(),
                    'interestRate' => $loan->getInterestRate(),
                    'insuranceRate' => $loan->getInsuranceRate(),
                    'durationYears' => $loan->getDurationYears(),
                    'startDate' => $loan->getStartDate()->format('Y-m-d'),
                    'deferredMonths' => $loan->getDeferredMonths(),
                    'monthlyPayment' => $loanStats['monthlyPayment'],
                    'totalCost' => $loanStats['totalCost'],
                    'totalInterest' => $loanStats['totalInterest']
                ]
            ]);
        } catch (\Exception $e) {
            return new JsonResponse([
                'success' => false,
                'message' => 'Erreur lors de l\'ajout du prêt: ' . $e->getMessage()
            ]);
        }
    }

    #[Route('/{id}/loan/{loanId}/edit', name: 'app_project_loan_edit', methods: ['POST'])]
    public function editLoan(Request $request, Project $project, int $loanId): JsonResponse
    {
        $this->denyAccessUnlessGranted('edit', $project);

        $loan = $this->entityManager->getRepository(Loan::class)->find($loanId);
        
        if (!$loan || $loan->getProject() !== $project) {
            return new JsonResponse(['success' => false, 'message' => 'Prêt non trouvé']);
        }

        try {
            $field = $request->request->get('field');
            $value = $request->request->get('value');

            switch ($field) {
                case 'amount':
                    $loan->setAmount($value);
                    break;
                case 'interestRate':
                    $loan->setInterestRate($value);
                    break;
                case 'insuranceRate':
                    $loan->setInsuranceRate($value);
                    break;
                case 'durationYears':
                    $loan->setDurationYears((int)$value);
                    break;
                case 'startDate':
                    $loan->setStartDate(new \DateTime($value));
                    break;
                case 'deferredMonths':
                    $loan->setDeferredMonths((int)$value);
                    break;
                default:
                    return new JsonResponse(['success' => false, 'message' => 'Champ non reconnu']);
            }

            $this->entityManager->flush();

            return new JsonResponse([
                'success' => true,
                'message' => 'Prêt mis à jour avec succès',
                'value' => $value
            ]);
        } catch (\Exception $e) {
            return new JsonResponse([
                'success' => false,
                'message' => 'Erreur lors de la mise à jour: ' . $e->getMessage()
            ]);
        }
    }

    #[Route('/{id}/loan/{loanId}/delete', name: 'app_project_loan_delete', methods: ['DELETE'])]
    public function deleteLoan(Project $project, int $loanId): JsonResponse
    {
        $this->denyAccessUnlessGranted('edit', $project);

        $loan = $this->entityManager->getRepository(Loan::class)->find($loanId);
        
        if (!$loan || $loan->getProject() !== $project) {
            return new JsonResponse(['success' => false, 'message' => 'Prêt non trouvé']);
        }

        try {
            $this->entityManager->remove($loan);
            $this->entityManager->flush();

            return new JsonResponse([
                'success' => true,
                'message' => 'Prêt supprimé avec succès'
            ]);
        } catch (\Exception $e) {
            return new JsonResponse([
                'success' => false,
                'message' => 'Erreur lors de la suppression: ' . $e->getMessage()
            ]);
        }
    }

    #[Route('/{id}/charge/add', name: 'app_project_charge_add', methods: ['POST'])]
    public function addCharge(Request $request, Project $project): JsonResponse
    {
        $this->denyAccessUnlessGranted('edit', $project);

        try {
            $charge = new Charge();
            $charge->setProject($project);
            $charge->setLabel($request->request->get('label'));
            $charge->setAmount($request->request->get('amount'));
            $charge->setType($request->request->get('type'));
            $charge->setIsRecursive($request->request->get('type') === Charge::TYPE_RECURRING);
            $charge->setStartDate(new \DateTime($request->request->get('startDate')));

            if ($request->request->get('frequency')) {
                $charge->setFrequency($request->request->get('frequency'));
            }

            if ($request->request->get('description')) {
                $charge->setDescription($request->request->get('description'));
            }

            $this->entityManager->persist($charge);
            $this->entityManager->flush();

            return new JsonResponse([
                'success' => true,
                'message' => 'Charge ajoutée avec succès',
                'charge' => [
                    'id' => $charge->getId(),
                    'label' => $charge->getLabel(),
                    'amount' => $charge->getAmount(),
                    'type' => $charge->getType(),
                    'frequency' => $charge->getFrequency(),
                    'description' => $charge->getDescription(),
                    'startDate' => $charge->getStartDate()->format('Y-m-d')
                ]
            ]);
        } catch (\Exception $e) {
            return new JsonResponse([
                'success' => false,
                'message' => 'Erreur lors de l\'ajout de la charge: ' . $e->getMessage()
            ]);
        }
    }

    #[Route('/{id}/charge/{chargeId}/edit', name: 'app_project_charge_edit', methods: ['POST'])]
    public function editCharge(Request $request, Project $project, int $chargeId): JsonResponse
    {
        $this->denyAccessUnlessGranted('edit', $project);

        $charge = $this->entityManager->getRepository(Charge::class)->find($chargeId);

        if (!$charge || $charge->getProject() !== $project) {
            return new JsonResponse(['success' => false, 'message' => 'Charge non trouvée']);
        }

        try {
            $field = $request->request->get('field');
            $value = $request->request->get('value');

            switch ($field) {
                case 'label':
                    $charge->setLabel($value);
                    break;
                case 'amount':
                    $charge->setAmount($value);
                    break;
                case 'type':
                    $charge->setType($value);
                    $charge->setIsRecursive($value === Charge::TYPE_RECURRING);
                    break;
                case 'frequency':
                    $charge->setFrequency($value);
                    break;
                case 'description':
                    $charge->setDescription($value);
                    break;
                case 'startDate':
                    $charge->setStartDate(new \DateTime($value));
                    break;
                default:
                    return new JsonResponse(['success' => false, 'message' => 'Champ non reconnu']);
            }

            $this->entityManager->flush();

            return new JsonResponse([
                'success' => true,
                'message' => 'Charge mise à jour avec succès',
                'value' => $value
            ]);
        } catch (\Exception $e) {
            return new JsonResponse([
                'success' => false,
                'message' => 'Erreur lors de la mise à jour: ' . $e->getMessage()
            ]);
        }
    }

    #[Route('/{id}/charge/{chargeId}/delete', name: 'app_project_charge_delete', methods: ['DELETE'])]
    public function deleteCharge(Project $project, int $chargeId): JsonResponse
    {
        $this->denyAccessUnlessGranted('edit', $project);

        $charge = $this->entityManager->getRepository(Charge::class)->find($chargeId);

        if (!$charge || $charge->getProject() !== $project) {
            return new JsonResponse(['success' => false, 'message' => 'Charge non trouvée']);
        }

        try {
            $this->entityManager->remove($charge);
            $this->entityManager->flush();

            return new JsonResponse([
                'success' => true,
                'message' => 'Charge supprimée avec succès'
            ]);
        } catch (\Exception $e) {
            return new JsonResponse([
                'success' => false,
                'message' => 'Erreur lors de la suppression: ' . $e->getMessage()
            ]);
        }
    }

    #[Route('/{id}/delete', name: 'app_project_delete', methods: ['DELETE'])]
    public function delete(Project $project): JsonResponse
    {
        $this->denyAccessUnlessGranted('delete', $project);

        try {
            $this->entityManager->remove($project);
            $this->entityManager->flush();

            return new JsonResponse([
                'success' => true,
                'message' => 'Projet supprimé avec succès'
            ]);
        } catch (\Exception $e) {
            return new JsonResponse([
                'success' => false,
                'message' => 'Erreur lors de la suppression: ' . $e->getMessage()
            ]);
        }
    }

    #[Route('/{id}/loan/{loanId}/calculate', name: 'app_project_loan_calculate', methods: ['GET'])]
    public function calculateLoan(Project $project, int $loanId): JsonResponse
    {
        $this->denyAccessUnlessGranted('view', $project);

        $loan = $this->entityManager->getRepository(Loan::class)->find($loanId);

        if (!$loan || $loan->getProject() !== $project) {
            return new JsonResponse(['success' => false, 'message' => 'Prêt non trouvé']);
        }

        try {
            $stats = $this->loanCalculator->calculateLoanStatistics($loan);
            $schedule = $this->loanCalculator->generateAmortizationSchedule($loan); // Full schedule

            return new JsonResponse([
                'success' => true,
                'statistics' => $stats,
                'schedule' => $schedule
            ]);
        } catch (\Exception $e) {
            return new JsonResponse([
                'success' => false,
                'message' => 'Erreur lors du calcul: ' . $e->getMessage()
            ]);
        }
    }

    #[Route('/{id}/loans/summary', name: 'app_project_loans_summary', methods: ['GET'])]
    public function getLoansSummary(Project $project): JsonResponse
    {
        $this->denyAccessUnlessGranted('view', $project);

        try {
            $loans = $project->getLoans()->toArray();

            if (empty($loans)) {
                return new JsonResponse([
                    'success' => true,
                    'summary' => [
                        'totalPrincipal' => 0,
                        'totalMonthlyPayment' => 0,
                        'totalCost' => 0,
                        'totalInterest' => 0,
                        'count' => 0
                    ]
                ]);
            }

            $stats = $this->loanCalculator->calculateMultipleLoansStatistics($loans);

            return new JsonResponse([
                'success' => true,
                'summary' => $stats['summary'],
                'loans' => $stats['loans']
            ]);
        } catch (\Exception $e) {
            return new JsonResponse([
                'success' => false,
                'message' => 'Erreur lors du calcul: ' . $e->getMessage()
            ]);
        }
    }

    #[Route('/{id}/expenses/summary', name: 'app_project_expenses_summary', methods: ['GET'])]
    public function getExpensesSummary(Project $project): JsonResponse
    {
        $this->denyAccessUnlessGranted('view', $project);

        try {
            $stats = $this->expenseCalculator->getExpenseStatistics($project);

            return new JsonResponse([
                'success' => true,
                'statistics' => $stats
            ]);
        } catch (\Exception $e) {
            return new JsonResponse([
                'success' => false,
                'message' => 'Erreur lors du calcul: ' . $e->getMessage()
            ]);
        }
    }

    #[Route('/{id}/expenses/projections', name: 'app_project_expenses_projections', methods: ['GET'])]
    public function getExpensesProjections(Request $request, Project $project): JsonResponse
    {
        $this->denyAccessUnlessGranted('view', $project);

        try {
            $years = (int) $request->query->get('years', 10);
            $projections = $this->expenseCalculator->calculateExpenseProjections($project, $years);

            return new JsonResponse([
                'success' => true,
                'projections' => $projections
            ]);
        } catch (\Exception $e) {
            return new JsonResponse([
                'success' => false,
                'message' => 'Erreur lors du calcul: ' . $e->getMessage()
            ]);
        }
    }

    #[Route('/{id}/profitability', name: 'app_project_profitability', methods: ['GET'])]
    public function getProfitabilityAnalysis(Project $project): JsonResponse
    {
        $this->denyAccessUnlessGranted('view', $project);

        try {
            $analysis = $this->profitabilityCalculator->calculateProfitabilityAnalysis($project);

            return new JsonResponse([
                'success' => true,
                'analysis' => $analysis
            ]);
        } catch (\Exception $e) {
            return new JsonResponse([
                'success' => false,
                'message' => 'Erreur lors du calcul: ' . $e->getMessage()
            ]);
        }
    }

    #[Route('/{id}/profitability/projections', name: 'app_project_profitability_projections', methods: ['GET'])]
    public function getProfitabilityProjections(Request $request, Project $project): JsonResponse
    {
        $this->denyAccessUnlessGranted('view', $project);

        try {
            $years = (int) $request->query->get('years', 10);
            $projections = $this->profitabilityCalculator->calculateProjections($project, $years);

            return new JsonResponse([
                'success' => true,
                'projections' => $projections
            ]);
        } catch (\Exception $e) {
            return new JsonResponse([
                'success' => false,
                'message' => 'Erreur lors du calcul: ' . $e->getMessage()
            ]);
        }
    }
}
