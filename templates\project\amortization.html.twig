{% extends 'base.html.twig' %}

{% block title %}Tableaux d'amortissement - {{ project.name }} - OptiImmo{% endblock %}

{% block body %}
<div class="min-h-screen bg-gray-50 py-8">
    <div class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
        <!-- Header -->
        <div class="md:flex md:items-center md:justify-between mb-8">
            <div class="flex-1 min-w-0">
                <nav class="flex" aria-label="Breadcrumb">
                    <ol class="flex items-center space-x-4">
                        <li>
                            <a href="{{ path('app_project_index') }}" class="text-gray-400 hover:text-gray-500">
                                <svg class="flex-shrink-0 h-5 w-5" xmlns="http://www.w3.org/2000/svg" viewBox="0 0 20 20" fill="currentColor">
                                    <path d="M10.707 2.293a1 1 0 00-1.414 0l-7 7a1 1 0 001.414 1.414L4 10.414V17a1 1 0 001 1h2a1 1 0 001-1v-2a1 1 0 011-1h2a1 1 0 011 1v2a1 1 0 001 1h2a1 1 0 001-1v-6.586l.293.293a1 1 0 001.414-1.414l-7-7z" />
                                </svg>
                            </a>
                        </li>
                        <li>
                            <div class="flex items-center">
                                <svg class="flex-shrink-0 h-5 w-5 text-gray-300" xmlns="http://www.w3.org/2000/svg" fill="currentColor" viewBox="0 0 20 20">
                                    <path d="M5.555 17.776l8-16 .894.448-8 16-.894-.448z" />
                                </svg>
                                <a href="{{ path('app_project_show', {id: project.id}) }}" class="ml-4 text-sm font-medium text-gray-500 hover:text-gray-700">{{ project.name }}</a>
                            </div>
                        </li>
                        <li>
                            <div class="flex items-center">
                                <svg class="flex-shrink-0 h-5 w-5 text-gray-300" xmlns="http://www.w3.org/2000/svg" fill="currentColor" viewBox="0 0 20 20">
                                    <path d="M5.555 17.776l8-16 .894.448-8 16-.894-.448z" />
                                </svg>
                                <span class="ml-4 text-sm font-medium text-gray-500">Tableaux d'amortissement</span>
                            </div>
                        </li>
                    </ol>
                </nav>
                <h1 class="mt-2 text-2xl font-bold leading-7 text-gray-900 sm:text-3xl sm:truncate">
                    Tableaux d'amortissement
                </h1>
                <p class="mt-1 text-sm text-gray-500">
                    {{ project.name }} - {{ loans|length }} prêt(s)
                </p>
            </div>
        </div>

        {% if loans|length > 0 %}
            <!-- Controls -->
            <div class="bg-white shadow rounded-lg mb-6">
                <div class="px-6 py-4">
                    <div class="flex flex-col sm:flex-row sm:items-center sm:justify-between space-y-4 sm:space-y-0">
                        <div class="flex items-center space-x-4">
                            <label for="scheduleType" class="text-sm font-medium text-gray-700">Affichage :</label>
                            <select id="scheduleType" class="px-3 py-2 border border-gray-300 rounded-md text-sm focus:outline-none focus:ring-2 focus:ring-indigo-500">
                                <option value="combined">Tableau combiné (tous les prêts)</option>
                                {% for loan in loans %}
                                    <option value="{{ loan.id }}">Prêt {{ loop.index }} - {{ loan.amount|number_format(0, ',', ' ') }}€</option>
                                {% endfor %}
                            </select>
                        </div>
                        
                        <div class="flex items-center space-x-2">
                            <button onclick="exportToCSV()" class="px-4 py-2 bg-green-600 text-white rounded-md hover:bg-green-700 text-sm">
                                <svg class="-ml-1 mr-2 h-4 w-4 inline" xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 10v6m0 0l-3-3m3 3l3-3m2 8H7a2 2 0 01-2-2V5a2 2 0 012-2h5.586a1 1 0 01.707.293l5.414 5.414a1 1 0 01.293.707V19a2 2 0 01-2 2z" />
                                </svg>
                                Exporter CSV
                            </button>
                            <button onclick="printTable()" class="px-4 py-2 bg-blue-600 text-white rounded-md hover:bg-blue-700 text-sm">
                                <svg class="-ml-1 mr-2 h-4 w-4 inline" xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M17 17h2a2 2 0 002-2v-4a2 2 0 00-2-2H5a2 2 0 00-2 2v4a2 2 0 002 2h2m2 4h6a2 2 0 002-2v-4a2 2 0 00-2-2H9a2 2 0 00-2 2v4a2 2 0 002 2zm8-12V5a2 2 0 00-2-2H9a2 2 0 00-2 2v4h10z" />
                                </svg>
                                Imprimer
                            </button>
                        </div>
                    </div>
                </div>
            </div>

            <!-- Schedule Container -->
            <div class="bg-white shadow rounded-lg">
                <div id="scheduleContainer">
                    <div class="text-center py-12">
                        <div class="animate-spin rounded-full h-12 w-12 border-b-2 border-indigo-600 mx-auto"></div>
                        <p class="mt-4 text-sm text-gray-500">Chargement du tableau d'amortissement...</p>
                    </div>
                </div>
            </div>
        {% else %}
            <!-- Empty State -->
            <div class="bg-white shadow rounded-lg">
                <div class="text-center py-12">
                    <svg class="mx-auto h-12 w-12 text-gray-400" xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 8c-1.657 0-3 .895-3 2s1.343 2 3 2 3 .895 3 2-1.343 2-3 2m0-8c1.11 0 2.08.402 2.599 1M12 8V7m0 1v8m0 0v1m0-1c-1.11 0-2.08-.402-2.599-1" />
                    </svg>
                    <h3 class="mt-2 text-sm font-medium text-gray-900">Aucun prêt</h3>
                    <p class="mt-1 text-sm text-gray-500">Ajoutez des prêts à votre projet pour voir les tableaux d'amortissement.</p>
                    <div class="mt-6">
                        <a href="{{ path('app_project_show', {id: project.id}) }}" 
                           class="inline-flex items-center px-4 py-2 border border-transparent shadow-sm text-sm font-medium rounded-md text-white bg-indigo-600 hover:bg-indigo-700">
                            Retour au projet
                        </a>
                    </div>
                </div>
            </div>
        {% endif %}
    </div>
</div>

<script>
const projectId = {{ project.id }};
let currentScheduleData = null;

document.addEventListener('DOMContentLoaded', function() {
    loadSchedule();
    
    document.getElementById('scheduleType').addEventListener('change', function() {
        loadSchedule();
    });
});

function loadSchedule() {
    const scheduleType = document.getElementById('scheduleType').value;
    const container = document.getElementById('scheduleContainer');
    
    container.innerHTML = `
        <div class="text-center py-12">
            <div class="animate-spin rounded-full h-12 w-12 border-b-2 border-indigo-600 mx-auto"></div>
            <p class="mt-4 text-sm text-gray-500">Chargement du tableau d'amortissement...</p>
        </div>
    `;
    
    if (scheduleType === 'combined') {
        loadCombinedSchedule();
    } else {
        loadIndividualSchedule(scheduleType);
    }
}

function loadCombinedSchedule() {
    fetch(`/projet/${projectId}/loans/combined-schedule`)
        .then(response => response.json())
        .then(data => {
            if (data.success) {
                currentScheduleData = data;
                displayCombinedSchedule(data.schedule, data.loans);
            } else {
                showError(data.message);
            }
        })
        .catch(error => {
            console.error('Error:', error);
            showError('Erreur lors du chargement du tableau combiné');
        });
}

function loadIndividualSchedule(loanId) {
    fetch(`/projet/${projectId}/loan/${loanId}/calculate`)
        .then(response => response.json())
        .then(data => {
            if (data.success) {
                currentScheduleData = data;
                displayIndividualSchedule(data.schedule, data.statistics);
            } else {
                showError(data.message);
            }
        })
        .catch(error => {
            console.error('Error:', error);
            showError('Erreur lors du chargement du tableau');
        });
}

function displayCombinedSchedule(schedule, loans) {
    const container = document.getElementById('scheduleContainer');

    if (schedule.length === 0) {
        container.innerHTML = '<div class="text-center py-8 text-gray-500">Aucune donnée à afficher</div>';
        return;
    }

    // Create loans legend
    let loansLegend = '<div class="px-6 py-4 border-b border-gray-200"><h3 class="text-lg font-medium text-gray-900 mb-4">Légende des prêts</h3><div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4">';

    loans.forEach((loan, index) => {
        const colorClass = getColorClass(index);
        loansLegend += `
            <div class="flex items-center space-x-2">
                <div class="w-4 h-4 rounded ${colorClass}"></div>
                <span class="text-sm">Prêt ${index + 1}: ${parseFloat(loan.amount).toLocaleString('fr-FR', {minimumFractionDigits: 0, maximumFractionDigits: 0})}€</span>
            </div>
        `;
    });

    loansLegend += '</div></div>';

    // Create simplified table - only totals and key info
    let html = loansLegend + '<div class="overflow-x-auto"><table class="min-w-full divide-y divide-gray-200 text-sm"><thead class="bg-gray-50"><tr>';

    // Simplified headers
    html += '<th class="px-4 py-3 text-left font-medium text-gray-500 uppercase tracking-wider">Date</th>';
    html += '<th class="px-4 py-3 text-center font-medium text-gray-500 uppercase tracking-wider">Échéance Totale</th>';
    html += '<th class="px-4 py-3 text-center font-medium text-gray-500 uppercase tracking-wider">Capital Total</th>';
    html += '<th class="px-4 py-3 text-center font-medium text-gray-500 uppercase tracking-wider">Intérêts Totaux</th>';
    html += '<th class="px-4 py-3 text-center font-medium text-gray-500 uppercase tracking-wider">Assurance Totale</th>';
    html += '<th class="px-4 py-3 text-center font-medium text-gray-500 uppercase tracking-wider">Restant Dû Total</th>';
    html += '<th class="px-4 py-3 text-center font-medium text-gray-500 uppercase tracking-wider">Détail</th>';

    html += '</tr></thead><tbody class="bg-white divide-y divide-gray-200">';

    // Data rows - simplified
    schedule.forEach((row, index) => {
        // Check if any loan is in deferred period
        let hasDeferred = false;
        Object.values(row.loans).forEach(loanData => {
            if (loanData && loanData.isDeferred) hasDeferred = true;
        });

        const rowClass = hasDeferred ? 'bg-yellow-50' : '';
        const deferredBadge = hasDeferred ? '<span class="inline-flex items-center px-2 py-0.5 rounded text-xs font-medium bg-yellow-100 text-yellow-800 ml-2">Différé</span>' : '';

        html += `<tr class="hover:bg-gray-50 ${rowClass}">`;
        html += `<td class="px-4 py-3 whitespace-nowrap font-medium">${row.dateFormatted}${deferredBadge}</td>`;
        html += `<td class="px-4 py-3 whitespace-nowrap text-center font-semibold text-lg">${row.totals.payment.toLocaleString('fr-FR', {minimumFractionDigits: 0, maximumFractionDigits: 0})}€</td>`;
        html += `<td class="px-4 py-3 whitespace-nowrap text-center">${row.totals.principal.toLocaleString('fr-FR', {minimumFractionDigits: 0, maximumFractionDigits: 0})}€</td>`;
        html += `<td class="px-4 py-3 whitespace-nowrap text-center">${row.totals.interest.toLocaleString('fr-FR', {minimumFractionDigits: 0, maximumFractionDigits: 0})}€</td>`;
        html += `<td class="px-4 py-3 whitespace-nowrap text-center">${row.totals.insurance.toLocaleString('fr-FR', {minimumFractionDigits: 0, maximumFractionDigits: 0})}€</td>`;
        html += `<td class="px-4 py-3 whitespace-nowrap text-center font-medium">${row.totals.balance.toLocaleString('fr-FR', {minimumFractionDigits: 0, maximumFractionDigits: 0})}€</td>`;
        html += `<td class="px-4 py-3 whitespace-nowrap text-center">
                    <button onclick="toggleRowDetail(${index})" class="text-indigo-600 hover:text-indigo-800 text-xs">
                        <span id="toggle-${index}">Voir détail</span>
                    </button>
                 </td>`;
        html += '</tr>';

        // Hidden detail row
        html += `<tr id="detail-${index}" class="hidden bg-gray-50">`;
        html += '<td colspan="7" class="px-4 py-3">';
        html += '<div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4">';

        loans.forEach((loan, loanIndex) => {
            const loanData = row.loans[loan.id];
            if (loanData) {
                const colorClass = getColorClass(loanIndex);
                const deferredText = loanData.isDeferred ? ' (Différé)' : '';
                html += `
                    <div class="border rounded p-3 bg-white">
                        <div class="flex items-center mb-2">
                            <div class="w-3 h-3 rounded ${colorClass} mr-2"></div>
                            <span class="font-medium text-sm">Prêt ${loanIndex + 1}${deferredText}</span>
                        </div>
                        <div class="text-xs space-y-1">
                            <div>Échéance: <span class="font-medium">${loanData.payment.toLocaleString('fr-FR', {minimumFractionDigits: 0, maximumFractionDigits: 0})}€</span></div>
                            <div>Capital: <span class="font-medium">${loanData.principal.toLocaleString('fr-FR', {minimumFractionDigits: 0, maximumFractionDigits: 0})}€</span></div>
                            <div>Intérêts: <span class="font-medium">${loanData.interest.toLocaleString('fr-FR', {minimumFractionDigits: 0, maximumFractionDigits: 0})}€</span></div>
                            <div>Assurance: <span class="font-medium">${loanData.insurance.toLocaleString('fr-FR', {minimumFractionDigits: 0, maximumFractionDigits: 0})}€</span></div>
                            <div>Restant: <span class="font-medium">${loanData.balance.toLocaleString('fr-FR', {minimumFractionDigits: 0, maximumFractionDigits: 0})}€</span></div>
                        </div>
                    </div>
                `;
            }
        });

        html += '</div></td></tr>';
    });

    html += '</tbody></table></div>';

    container.innerHTML = html;
}

// Function to toggle row detail
function toggleRowDetail(index) {
    const detailRow = document.getElementById(`detail-${index}`);
    const toggleText = document.getElementById(`toggle-${index}`);

    if (detailRow.classList.contains('hidden')) {
        detailRow.classList.remove('hidden');
        toggleText.textContent = 'Masquer détail';
    } else {
        detailRow.classList.add('hidden');
        toggleText.textContent = 'Voir détail';
    }
}

function displayIndividualSchedule(schedule, stats) {
    const container = document.getElementById('scheduleContainer');

    let html = `
        <div class="px-6 py-4 border-b border-gray-200">
            <div class="grid grid-cols-2 md:grid-cols-4 gap-4 text-sm">
                <div><span class="font-medium">Mensualité:</span> ${stats.monthlyPayment.toLocaleString('fr-FR', {minimumFractionDigits: 0, maximumFractionDigits: 0})} €</div>
                <div><span class="font-medium">Annuité:</span> ${(stats.monthlyPayment * 12).toLocaleString('fr-FR', {minimumFractionDigits: 0, maximumFractionDigits: 0})} €</div>
                <div><span class="font-medium">Coût total:</span> ${stats.totalCost.toLocaleString('fr-FR', {minimumFractionDigits: 0, maximumFractionDigits: 0})} €</div>
                <div><span class="font-medium">Intérêts totaux:</span> ${stats.totalInterest.toLocaleString('fr-FR', {minimumFractionDigits: 0, maximumFractionDigits: 0})} €</div>
            </div>
        </div>

        <div class="overflow-x-auto">
            <table class="min-w-full divide-y divide-gray-200 text-sm">
                <thead class="bg-gray-50">
                    <tr>
                        <th class="px-4 py-3 text-left font-medium text-gray-500 uppercase tracking-wider">Mois</th>
                        <th class="px-4 py-3 text-left font-medium text-gray-500 uppercase tracking-wider">Date</th>
                        <th class="px-4 py-3 text-left font-medium text-gray-500 uppercase tracking-wider">Dette restante</th>
                        <th class="px-4 py-3 text-left font-medium text-gray-500 uppercase tracking-wider">Intérêts</th>
                        <th class="px-4 py-3 text-left font-medium text-gray-500 uppercase tracking-wider">Amortissement</th>
                        <th class="px-4 py-3 text-left font-medium text-gray-500 uppercase tracking-wider">Assurance</th>
                        <th class="px-4 py-3 text-left font-medium text-gray-500 uppercase tracking-wider">Échéance totale</th>
                    </tr>
                </thead>
                <tbody class="bg-white divide-y divide-gray-200">
    `;

    schedule.forEach(row => {
        const rowClass = row.isDeferred ? 'bg-yellow-50' : '';
        const deferredBadge = row.isDeferred ? '<span class="inline-flex items-center px-2 py-0.5 rounded text-xs font-medium bg-yellow-100 text-yellow-800 ml-1">Différé</span>' : '';

        // Format date if available
        let dateFormatted = '';
        if (row.date) {
            const date = new Date(row.date);
            dateFormatted = date.toLocaleDateString('fr-FR', { month: 'short', year: 'numeric' });
        }

        html += `
            <tr class="${rowClass}">
                <td class="px-4 py-3 whitespace-nowrap">${row.month}${deferredBadge}</td>
                <td class="px-4 py-3 whitespace-nowrap text-sm text-gray-600">${dateFormatted}</td>
                <td class="px-4 py-3 whitespace-nowrap font-medium">${row.balance.toLocaleString('fr-FR', {minimumFractionDigits: 0, maximumFractionDigits: 0})} €</td>
                <td class="px-4 py-3 whitespace-nowrap">${row.interest.toLocaleString('fr-FR', {minimumFractionDigits: 0, maximumFractionDigits: 0})} €</td>
                <td class="px-4 py-3 whitespace-nowrap">${row.principal.toLocaleString('fr-FR', {minimumFractionDigits: 0, maximumFractionDigits: 0})} €</td>
                <td class="px-4 py-3 whitespace-nowrap">${row.insurance.toLocaleString('fr-FR', {minimumFractionDigits: 0, maximumFractionDigits: 0})} €</td>
                <td class="px-4 py-3 whitespace-nowrap font-semibold text-lg">${row.payment.toLocaleString('fr-FR', {minimumFractionDigits: 0, maximumFractionDigits: 0})} €</td>
            </tr>
        `;
    });

    html += '</tbody></table></div>';

    container.innerHTML = html;
}

function getColorClass(index) {
    const colors = ['bg-blue-500', 'bg-green-500', 'bg-yellow-500', 'bg-red-500', 'bg-purple-500', 'bg-pink-500'];
    return colors[index % colors.length];
}

function showError(message) {
    document.getElementById('scheduleContainer').innerHTML = `
        <div class="text-center py-8 text-red-600">
            <svg class="mx-auto h-12 w-12 mb-4" xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 8v4m0 4h.01M21 12a9 9 0 11-18 0 9 9 0 0118 0z" />
            </svg>
            <p>${message}</p>
        </div>
    `;
}

function exportToCSV() {
    // Implementation for CSV export
    alert('Fonctionnalité d\'export CSV en cours de développement');
}

function printTable() {
    window.print();
}
</script>

<style>
@media print {
    .no-print { display: none !important; }
    body { font-size: 12px; }
    table { font-size: 10px; }
}
</style>
{% endblock %}
