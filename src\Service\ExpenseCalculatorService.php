<?php

namespace App\Service;

use App\Entity\Charge;
use App\Entity\Project;

class ExpenseCalculatorService
{
    /**
     * Calculate monthly expense for a charge based on its frequency
     */
    public function calculateMonthlyExpense(Charge $charge): float
    {
        $amount = (float) $charge->getAmount();
        
        if ($charge->getType() === Charge::TYPE_INITIAL) {
            return 0; // Initial charges are one-time, not monthly
        }
        
        return match($charge->getFrequency()) {
            Charge::FREQUENCY_WEEKLY => $amount * 52 / 12, // Weekly to monthly
            Charge::FREQUENCY_MONTHLY => $amount,
            Charge::FREQUENCY_QUARTERLY => $amount / 3,
            Charge::FREQUENCY_YEARLY => $amount / 12,
            default => $amount // Default to monthly
        };
    }

    /**
     * Calculate yearly expense for a charge
     */
    public function calculateYearlyExpense(Charge $charge): float
    {
        $amount = (float) $charge->getAmount();
        
        if ($charge->getType() === Charge::TYPE_INITIAL) {
            return 0; // Initial charges are one-time, not yearly recurring
        }
        
        return match($charge->getFrequency()) {
            Charge::FREQUENCY_WEEKLY => $amount * 52,
            Charge::FREQUENCY_MONTHLY => $amount * 12,
            Charge::FREQUENCY_QUARTERLY => $amount * 4,
            Charge::FREQUENCY_YEARLY => $amount,
            default => $amount * 12 // Default to monthly
        };
    }

    /**
     * Calculate total initial expenses for a project
     */
    public function calculateTotalInitialExpenses(Project $project): float
    {
        $total = 0;
        
        foreach ($project->getCharges() as $charge) {
            if ($charge->getType() === Charge::TYPE_INITIAL) {
                $total += (float) $charge->getAmount();
            }
        }
        
        return $total;
    }

    /**
     * Calculate total monthly recurring expenses for a project
     */
    public function calculateTotalMonthlyExpenses(Project $project): float
    {
        $total = 0;
        
        foreach ($project->getCharges() as $charge) {
            if ($charge->getType() === Charge::TYPE_RECURRING) {
                $total += $this->calculateMonthlyExpense($charge);
            }
        }
        
        return $total;
    }

    /**
     * Calculate total yearly recurring expenses for a project
     */
    public function calculateTotalYearlyExpenses(Project $project): float
    {
        $total = 0;
        
        foreach ($project->getCharges() as $charge) {
            if ($charge->getType() === Charge::TYPE_RECURRING) {
                $total += $this->calculateYearlyExpense($charge);
            }
        }
        
        return $total;
    }

    /**
     * Get expenses breakdown by category
     */
    public function getExpensesBreakdown(Project $project): array
    {
        $initialExpenses = [];
        $recurringExpenses = [];
        $totalInitial = 0;
        $totalMonthlyRecurring = 0;
        $totalYearlyRecurring = 0;

        foreach ($project->getCharges() as $charge) {
            $chargeData = [
                'id' => $charge->getId(),
                'label' => $charge->getLabel(),
                'amount' => (float) $charge->getAmount(),
                'type' => $charge->getType(),
                'frequency' => $charge->getFrequency(),
                'description' => $charge->getDescription(),
                'startDate' => $charge->getStartDate()
            ];

            if ($charge->getType() === Charge::TYPE_INITIAL) {
                $chargeData['totalAmount'] = (float) $charge->getAmount();
                $initialExpenses[] = $chargeData;
                $totalInitial += (float) $charge->getAmount();
            } else {
                $chargeData['monthlyAmount'] = $this->calculateMonthlyExpense($charge);
                $chargeData['yearlyAmount'] = $this->calculateYearlyExpense($charge);
                $recurringExpenses[] = $chargeData;
                $totalMonthlyRecurring += $chargeData['monthlyAmount'];
                $totalYearlyRecurring += $chargeData['yearlyAmount'];
            }
        }

        return [
            'initial' => [
                'expenses' => $initialExpenses,
                'total' => $totalInitial,
                'count' => count($initialExpenses)
            ],
            'recurring' => [
                'expenses' => $recurringExpenses,
                'totalMonthly' => $totalMonthlyRecurring,
                'totalYearly' => $totalYearlyRecurring,
                'count' => count($recurringExpenses)
            ],
            'summary' => [
                'totalInitial' => $totalInitial,
                'totalMonthlyRecurring' => $totalMonthlyRecurring,
                'totalYearlyRecurring' => $totalYearlyRecurring,
                'totalExpenses' => count($initialExpenses) + count($recurringExpenses)
            ]
        ];
    }

    /**
     * Calculate expense projections over time
     */
    public function calculateExpenseProjections(Project $project, int $years = 10): array
    {
        $breakdown = $this->getExpensesBreakdown($project);
        $projections = [];
        
        $initialTotal = $breakdown['initial']['total'];
        $monthlyRecurring = $breakdown['recurring']['totalMonthly'];
        
        for ($year = 1; $year <= $years; $year++) {
            $yearlyRecurring = $monthlyRecurring * 12;
            $cumulativeRecurring = $yearlyRecurring * $year;
            $totalCumulative = $initialTotal + $cumulativeRecurring;
            
            $projections[] = [
                'year' => $year,
                'yearlyRecurring' => $yearlyRecurring,
                'cumulativeRecurring' => $cumulativeRecurring,
                'totalCumulative' => $totalCumulative
            ];
        }
        
        return $projections;
    }

    /**
     * Get expense statistics
     */
    public function getExpenseStatistics(Project $project): array
    {
        $breakdown = $this->getExpensesBreakdown($project);
        
        // Calculate frequency distribution
        $frequencyDistribution = [];
        foreach ($breakdown['recurring']['expenses'] as $expense) {
            $frequency = $expense['frequency'] ?? 'monthly';
            $frequencyDistribution[$frequency] = ($frequencyDistribution[$frequency] ?? 0) + 1;
        }
        
        // Calculate average expense amounts
        $avgInitialExpense = $breakdown['initial']['count'] > 0 
            ? $breakdown['initial']['total'] / $breakdown['initial']['count'] 
            : 0;
            
        $avgMonthlyExpense = $breakdown['recurring']['count'] > 0 
            ? $breakdown['recurring']['totalMonthly'] / $breakdown['recurring']['count'] 
            : 0;
        
        return [
            'breakdown' => $breakdown,
            'frequencyDistribution' => $frequencyDistribution,
            'averages' => [
                'initialExpense' => $avgInitialExpense,
                'monthlyExpense' => $avgMonthlyExpense
            ],
            'ratios' => [
                'initialToRecurringRatio' => $breakdown['recurring']['totalYearly'] > 0 
                    ? $breakdown['initial']['total'] / $breakdown['recurring']['totalYearly'] 
                    : 0,
                'monthlyToYearlyRatio' => $breakdown['recurring']['totalYearly'] > 0 
                    ? $breakdown['recurring']['totalMonthly'] / ($breakdown['recurring']['totalYearly'] / 12) 
                    : 1
            ]
        ];
    }

    /**
     * Format frequency for display
     */
    public function formatFrequency(string $frequency): string
    {
        return match($frequency) {
            Charge::FREQUENCY_WEEKLY => 'Hebdomadaire',
            Charge::FREQUENCY_MONTHLY => 'Mensuelle',
            Charge::FREQUENCY_QUARTERLY => 'Trimestrielle',
            Charge::FREQUENCY_YEARLY => 'Annuelle',
            default => 'Mensuelle'
        };
    }

    /**
     * Get next payment date for a recurring charge
     */
    public function getNextPaymentDate(Charge $charge): ?\DateTimeInterface
    {
        if ($charge->getType() === Charge::TYPE_INITIAL) {
            return null; // Initial charges don't have recurring payments
        }
        
        $startDate = $charge->getStartDate();
        $now = new \DateTime();
        
        if ($startDate > $now) {
            return $startDate; // First payment is in the future
        }
        
        $interval = match($charge->getFrequency()) {
            Charge::FREQUENCY_WEEKLY => new \DateInterval('P1W'),
            Charge::FREQUENCY_MONTHLY => new \DateInterval('P1M'),
            Charge::FREQUENCY_QUARTERLY => new \DateInterval('P3M'),
            Charge::FREQUENCY_YEARLY => new \DateInterval('P1Y'),
            default => new \DateInterval('P1M')
        };
        
        $nextDate = clone $startDate;
        while ($nextDate <= $now) {
            $nextDate->add($interval);
        }
        
        return $nextDate;
    }
}
