import { Controller } from "@hotwired/stimulus"

export default class extends Controller {
    static targets = ["modal", "form", "container"]
    static values = { 
        projectId: Number,
        url: String 
    }

    connect() {
        console.log("Project controller connected")
    }

    // Modal management
    openModal() {
        this.modalTarget.classList.remove('hidden')
        const firstInput = this.modalTarget.querySelector('input, select, textarea')
        if (firstInput) {
            firstInput.focus()
        }
    }

    closeModal() {
        this.modalTarget.classList.add('hidden')
        if (this.hasFormTarget) {
            this.formTarget.reset()
        }
    }

    // Handle modal backdrop clicks
    handleBackdropClick(event) {
        if (event.target === this.modalTarget) {
            this.closeModal()
        }
    }

    // Handle form submission
    async submitForm(event) {
        event.preventDefault()
        
        const formData = new FormData(this.formTarget)
        
        try {
            const response = await fetch(this.urlValue, {
                method: 'POST',
                body: formData,
                headers: {
                    'X-Requested-With': 'XMLHttpRequest'
                }
            })

            if (response.ok) {
                const data = await response.json()
                
                if (data.success) {
                    this.closeModal()
                    this.showNotification(data.message, 'success')
                    
                    // Reload the page or update the container
                    if (this.hasContainerTarget) {
                        this.refreshContainer()
                    } else {
                        window.location.reload()
                    }
                } else {
                    this.showNotification(data.message || 'Une erreur est survenue', 'error')
                }
            } else if (response.redirected) {
                window.location.href = response.url
            } else {
                throw new Error('Network response was not ok')
            }
        } catch (error) {
            console.error('Error:', error)
            this.showNotification('Une erreur est survenue', 'error')
        }
    }

    // Delete item
    async delete(event) {
        const itemId = event.currentTarget.dataset.itemId
        const itemType = event.currentTarget.dataset.itemType || 'item'
        
        if (!confirm(`Êtes-vous sûr de vouloir supprimer cet ${itemType} ?`)) {
            return
        }

        try {
            const response = await fetch(`${this.urlValue}/${itemId}/delete`, {
                method: 'DELETE',
                headers: {
                    'X-Requested-With': 'XMLHttpRequest'
                }
            })

            const data = await response.json()
            
            if (data.success) {
                this.showNotification(data.message, 'success')
                
                // Remove the item from DOM or reload
                const itemElement = event.currentTarget.closest('[data-item-id]')
                if (itemElement) {
                    itemElement.remove()
                } else {
                    window.location.reload()
                }
            } else {
                this.showNotification(data.message || 'Erreur lors de la suppression', 'error')
            }
        } catch (error) {
            console.error('Error:', error)
            this.showNotification('Une erreur est survenue', 'error')
        }
    }

    // Refresh container content
    async refreshContainer() {
        if (!this.hasContainerTarget) return

        try {
            const response = await fetch(window.location.href, {
                headers: {
                    'X-Requested-With': 'XMLHttpRequest'
                }
            })
            
            if (response.ok) {
                const html = await response.text()
                const parser = new DOMParser()
                const doc = parser.parseFromString(html, 'text/html')
                const newContainer = doc.querySelector('[data-project-target="container"]')
                
                if (newContainer) {
                    this.containerTarget.innerHTML = newContainer.innerHTML
                }
            }
        } catch (error) {
            console.error('Error refreshing container:', error)
            window.location.reload()
        }
    }

    // Show notification
    showNotification(message, type = 'info') {
        // Create notification element
        const notification = document.createElement('div')
        notification.className = `fixed top-4 right-4 z-50 p-4 rounded-md shadow-lg transition-all duration-300 ${
            type === 'success' ? 'bg-green-500 text-white' : 
            type === 'error' ? 'bg-red-500 text-white' : 
            'bg-blue-500 text-white'
        }`
        notification.textContent = message

        document.body.appendChild(notification)

        // Auto remove after 5 seconds
        setTimeout(() => {
            notification.style.opacity = '0'
            setTimeout(() => {
                if (notification.parentNode) {
                    notification.parentNode.removeChild(notification)
                }
            }, 300)
        }, 5000)

        // Allow manual close
        notification.addEventListener('click', () => {
            notification.style.opacity = '0'
            setTimeout(() => {
                if (notification.parentNode) {
                    notification.parentNode.removeChild(notification)
                }
            }, 300)
        })
    }

    // Handle escape key
    handleKeydown(event) {
        if (event.key === 'Escape') {
            this.closeModal()
        }
    }
}
