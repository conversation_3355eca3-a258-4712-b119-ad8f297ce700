<?php
namespace App\Entity;

use App\Repository\ProjectRepository;
use Doctrine\Common\Collections\ArrayCollection;
use Doctrine\Common\Collections\Collection;
use Doctrine\ORM\Mapping as ORM;

#[ORM\Entity(repositoryClass: ProjectRepository::class)]
class Project
{
    #[ORM\Id]
    #[ORM\GeneratedValue]
    #[ORM\Column(type: 'integer')]
    private ?int $id = null;

    #[ORM\ManyToOne(targetEntity: User::class, inversedBy: 'projetsImmobiliers')]
    #[ORM\JoinColumn(nullable: false)]
    private ?User $user = null;

    #[ORM\Column(type: 'string', length: 255)]
    private string $name;

    #[ORM\Column(type: 'datetime')]
    private DateTimeInterface $createdAt;

    #[ORM\Column(type: 'decimal', precision: 10, scale: 2, nullable: true)]
    private ?string $rentAmount = null;

    #[ORM\Column(type: 'decimal', precision: 5, scale: 2, nullable: true)]
    private ?string $vacancyRate = null;

    #[ORM\Column(type: 'decimal', precision: 10, scale: 2, nullable: true)]
    private ?string $landPrice = null;

    #[ORM\Column(type: 'decimal', precision: 10, scale: 2, nullable: true)]
    private ?string $propertyValue = null;

    #[ORM\Column(type: 'integer', nullable: true)]
    private ?int $amortizationDuration = null;

    #[ORM\OneToMany(targetEntity: Loan::class, mappedBy: 'project', cascade: ['persist', 'remove'])]
    private Collection $loans;

    #[ORM\OneToMany(targetEntity: Charge::class, mappedBy: 'project', cascade: ['persist', 'remove'])]
    private Collection $charges;

    public function __construct()
    {
        $this->loans = new ArrayCollection();
        $this->charges = new ArrayCollection();
        $this->createdAt = new DateTimeImmutable();
    }

    public function getId(): ?int
    {
        return $this->id;
    }

    public function getUser(): ?User
    {
        return $this->user;
    }

    public function setUser(User $user): self
    {
        $this->user = $user;

        return $this;
    }

    public function getName(): string
    {
        return $this->name;
    }

    public function setName(string $name): self
    {
        $this->name = $name;

        return $this;
    }

    public function getCreatedAt(): DateTimeInterface
    {
        return $this->createdAt;
    }

    public function getRentAmount(): ?string
    {
        return $this->rentAmount;
    }

    public function setRentAmount(?string $rentAmount): self
    {
        $this->rentAmount = $rentAmount;

        return $this;
    }

    public function getVacancyRate(): ?string
    {
        return $this->vacancyRate;
    }

    public function setVacancyRate(?string $vacancyRate): self
    {
        $this->vacancyRate = $vacancyRate;

        return $this;
    }

    public function getLandPrice(): ?string
    {
        return $this->landPrice;
    }

    public function setLandPrice(?string $landPrice): self
    {
        $this->landPrice = $landPrice;

        return $this;
    }

    public function getPropertyValue(): ?string
    {
        return $this->propertyValue;
    }

    public function setPropertyValue(?string $propertyValue): self
    {
        $this->propertyValue = $propertyValue;

        return $this;
    }

    public function getAmortizationDuration(): ?int
    {
        return $this->amortizationDuration;
    }

    public function setAmortizationDuration(?int $amortizationDuration): self
    {
        $this->amortizationDuration = $amortizationDuration;

        return $this;
    }

    /**
     * @return Collection|Loan[]
     */
    public function getLoans(): Collection
    {
        return $this->loans;
    }

    public function addLoan(Loan $loan): self
    {
        if (!$this->loans->contains($loan)) {
            $this->loans->add($loan);
            $loan->setProject($this);
        }

        return $this;
    }

    public function removeLoan(Loan $loan): self
    {
        if ($this->loans->removeElement($loan)) {
            // set the owning side to null (unless already changed)
            if ($loan->getProject() === $this) {
                $loan->setProject(null);
            }
        }

        return $this;
    }

    /**
     * @return Collection|Charge[]
     */
    public function getCharges(): Collection
    {
        return $this->charges;
    }

    public function addCharge(Charge $charge): self
    {
        if (!$this->charges->contains($charge)) {
            $this->charges->add($charge);
            $charge->setProject($this);
        }

        return $this;
    }

    public function removeCharge(Charge $charge): self
    {
        if ($this->charges->removeElement($charge)) {
            if ($charge->getProject() === $this) {
                $charge->setProject(null);
            }
        }

        return $this;
    }
}
