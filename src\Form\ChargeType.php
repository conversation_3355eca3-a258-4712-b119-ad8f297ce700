<?php

namespace App\Form;

use App\Entity\Charge;
use Symfony\Component\Form\AbstractType;
use Symfony\Component\Form\Extension\Core\Type\TextType;
use Symfony\Component\Form\Extension\Core\Type\TextareaType;
use Symfony\Component\Form\Extension\Core\Type\NumberType;
use Symfony\Component\Form\Extension\Core\Type\ChoiceType;
use Symfony\Component\Form\Extension\Core\Type\DateType;
use Symfony\Component\Form\FormBuilderInterface;
use Symfony\Component\OptionsResolver\OptionsResolver;
use Symfony\Component\Validator\Constraints as Assert;

class ChargeType extends AbstractType
{
    public function buildForm(FormBuilderInterface $builder, array $options): void
    {
        $builder
            ->add('label', TextType::class, [
                'label' => 'Libellé',
                'attr' => [
                    'class' => 'w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-indigo-500',
                    'placeholder' => 'Ex: Taxe foncière, Assurance, etc.'
                ],
                'constraints' => [
                    new Assert\NotBlank(['message' => 'Le libellé est requis']),
                    new Assert\Length(['max' => 255])
                ]
            ])
            ->add('amount', NumberType::class, [
                'label' => 'Montant (€)',
                'attr' => [
                    'class' => 'w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-indigo-500',
                    'placeholder' => '0.00',
                    'step' => '0.01'
                ],
                'constraints' => [
                    new Assert\NotBlank(['message' => 'Le montant est requis']),
                    new Assert\Positive(['message' => 'Le montant doit être positif'])
                ]
            ])
            ->add('type', ChoiceType::class, [
                'label' => 'Type de charge',
                'choices' => Charge::getTypeChoices(),
                'attr' => [
                    'class' => 'w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-indigo-500'
                ],
                'constraints' => [
                    new Assert\NotBlank(['message' => 'Le type de charge est requis'])
                ]
            ])
            ->add('frequency', ChoiceType::class, [
                'label' => 'Fréquence',
                'choices' => Charge::getFrequencyChoices(),
                'required' => false,
                'placeholder' => 'Sélectionner une fréquence',
                'attr' => [
                    'class' => 'w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-indigo-500'
                ]
            ])
            ->add('startDate', DateType::class, [
                'label' => 'Date de début',
                'widget' => 'single_text',
                'attr' => [
                    'class' => 'w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-indigo-500'
                ],
                'constraints' => [
                    new Assert\NotBlank(['message' => 'La date de début est requise'])
                ]
            ])
            ->add('description', TextareaType::class, [
                'label' => 'Description',
                'required' => false,
                'attr' => [
                    'class' => 'w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-indigo-500',
                    'rows' => 3,
                    'placeholder' => 'Description optionnelle de la charge...'
                ]
            ]);
    }

    public function configureOptions(OptionsResolver $resolver): void
    {
        $resolver->setDefaults([
            'data_class' => Charge::class,
        ]);
    }
}
