{% extends 'base.html.twig' %}

{% block title %}Nouveau Projet - OptiImmo{% endblock %}

{% block body %}
<div class="min-h-screen bg-gray-50 py-8">
    <div class="max-w-md mx-auto">
        <div class="bg-white shadow rounded-lg p-6">
            <h2 class="text-2xl font-bold text-gray-900 text-center mb-6">Créer un nouveau projet</h2>
            
            <form method="post">
                <div class="mb-4">
                    <label for="name" class="block text-sm font-medium text-gray-700 mb-2">
                        Nom du projet
                    </label>
                    <input type="text"
                           id="name"
                           name="name"
                           required
                           class="w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-2 focus:ring-indigo-500 focus:border-indigo-500"
                           placeholder="Ex: Appartement Rue de la Paix">
                </div>

                <div class="mb-6">
                    <div class="flex items-center">
                        <input type="checkbox"
                               id="isSci"
                               name="isSci"
                               value="1"
                               class="rounded border-gray-300 text-indigo-600 shadow-sm focus:border-indigo-300 focus:ring focus:ring-indigo-200 focus:ring-opacity-50">
                        <label for="isSci" class="ml-2 block text-sm text-gray-700">
                            Projet dans une SCI
                        </label>
                    </div>
                    <p class="mt-1 text-xs text-gray-500">
                        Cochez cette case si le projet est détenu par une SCI (pour les futurs calculs d'imposition)
                    </p>
                </div>
                
                <div class="flex items-center justify-between">
                    <a href="{{ path('app_project_index') }}" 
                       class="px-4 py-2 bg-gray-300 text-gray-700 rounded-md hover:bg-gray-400 transition-colors">
                        Annuler
                    </a>
                    <button type="submit" 
                            class="px-4 py-2 bg-indigo-600 text-white rounded-md hover:bg-indigo-700 transition-colors">
                        Créer le projet
                    </button>
                </div>
            </form>
        </div>
    </div>
</div>
{% endblock %}
