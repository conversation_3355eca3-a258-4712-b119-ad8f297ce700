{% extends 'base.html.twig' %}

{% block title %}OptiImmo - Optimisez vos investissements immobiliers{% endblock %}

{% block body %}
<div class="bg-white ">
    <!-- Hero section -->
    <div class="relative py-8">
        <div class="max-w-7xl mx-auto sm:px-6 lg:px-8">
            <div class="relative shadow-xl sm:rounded-2xl sm:overflow-hidden">
                <div class="absolute inset-0">
                    <img class="h-full w-full object-cover" src="https://images.unsplash.com/photo-1560518883-ce09059eeffa?ixlib=rb-4.0.3&ixid=MnwxMjA3fDB8MHxwaG90by1wYWdlfHx8fGVufDB8fHx8&auto=format&fit=crop&w=1073&q=80" alt="Immobilier">
                    <div class="absolute inset-0 bg-gradient-to-r from-blue-800 to-indigo-700 mix-blend-multiply"></div>
                </div>
                <div class="relative px-4 py-16 sm:px-6 sm:py-24 lg:py-32 lg:px-8 pt-24">
                    <h1 class="text-center text-4xl font-extrabold tracking-tight sm:text-5xl lg:text-6xl">
                        <span class="block text-white">OptiImmo</span>
                        <span class="block text-indigo-200">Optimisez vos investissements immobiliers</span>
                    </h1>
                    <p class="mt-6 max-w-lg mx-auto text-center text-xl text-indigo-200 sm:max-w-3xl">
                        Analysez, planifiez et suivez vos projets immobiliers en temps réel. Prenez des décisions éclairées grâce à nos outils de simulation financière.
                    </p>
                    <div class="mt-10 max-w-sm mx-auto sm:max-w-none sm:flex sm:justify-center">
                    {% if not app.user %}
                        <div class="space-y-4 sm:space-y-0 sm:mx-auto sm:inline-grid sm:grid-cols-2 sm:gap-5">
                            <a href="{{ path('app_register') }}" class="flex items-center justify-center px-4 py-3 border border-transparent text-base font-medium rounded-md shadow-sm text-indigo-700 bg-white hover:bg-indigo-50 sm:px-8">
                                S'inscrire
                            </a>
                            <a href="{{ path('app_login') }}" class="flex items-center justify-center px-4 py-3 border border-transparent text-base font-medium rounded-md shadow-sm text-white bg-indigo-500 hover:bg-indigo-600 sm:px-8">
                                Se connecter
                            </a>
                        </div>
                    {% else %}
                        <div class="space-y-4 sm:space-y-0 sm:mx-auto sm:inline-grid sm:grid-cols-2 sm:gap-5">
                            <a href="{{ path('app_project_index') }}" class="flex items-center justify-center px-4 py-3 border border-transparent text-base font-medium rounded-md shadow-sm text-white bg-indigo-500 hover:bg-indigo-600 sm:px-8">
                                Mes Projets
                            </a>
                            <a href="{{ path('app_project_new') }}" class="flex items-center justify-center px-4 py-3 border border-transparent text-base font-medium rounded-md shadow-sm text-indigo-700 bg-white hover:bg-indigo-50 sm:px-8">
                                Créer un Projet
                            </a>
                        </div>
                    {% endif %}
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- Features section --><div class="py-16 bg-gray-50 overflow-hidden lg:py-24">
  <div class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
    <!-- Titres centré au-dessus des colonnes -->
    <div class="text-center max-w-3xl mx-auto">
      <h2 class="text-3xl font-extrabold tracking-tight text-gray-900 sm:text-4xl">
        Une solution complète pour vos investissements
      </h2>
      <p class="mt-4 text-xl text-gray-500">
        OptiImmo vous aide à prendre les meilleures décisions pour vos projets immobiliers.
      </p>
    </div>

    <!-- Grille à deux colonnes pour les features -->
    <div class="mt-12 grid grid-cols-1 md:grid-cols-2 gap-8">
      <!-- Colonne 1 : Analyse -->
      <div class="flex items-center">
        <!-- Icône à gauche -->
        <div class="flex-shrink-0">
          <div class="h-12 w-12 rounded-md bg-indigo-500 flex items-center justify-center text-white">
            <svg class="h-6 w-6" xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24" stroke="currentColor">
              <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2"
                    d="M9 19v-6a2 2 0 00-2-2H5a2 2 0 00-2 2v6a2 2 0 002 2h2a2 2 0 002-2zm0 0V9a2 2 0 012-2h2a2 2 0 012 2v10m-6 0a2 2 0 002 2h2a2 2 0 002-2m0 0V5a2 2 0 012-2h2a2 2 0 012 2v14a2 2 0 01-2 2h-2a2 2 0 01-2-2z" />
            </svg>
          </div>
        </div>
        <!-- Texte à droite, centré verticalement -->
        <div class="ml-6">
          <h3 class="text-2xl font-extrabold text-gray-900 tracking-tight sm:text-3xl">
            Analysez vos projets en détail
          </h3>
          <p class="mt-2 text-lg text-gray-500">
            Entrez les paramètres de votre projet et obtenez instantanément des indicateurs clés pour évaluer sa rentabilité.
          </p>
        </div>
      </div>

      <!-- Colonne 2 : Gestion -->
      <div class="flex items-center">
        <!-- Icône à gauche -->
        <div class="flex-shrink-0">
          <div class="h-12 w-12 rounded-md bg-indigo-500 flex items-center justify-center text-white">
            <svg class="h-6 w-6" xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24" stroke="currentColor">
              <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2"
                    d="M12 8v4l3 3m6-3a9 9 0 11-18 0 9 9 0 0118 0z" />
            </svg>
          </div>
        </div>
        <!-- Texte à droite, centré verticalement -->
        <div class="ml-6">
          <h3 class="text-2xl font-extrabold text-gray-900 tracking-tight sm:text-3xl">
            Gestion simplifiée
          </h3>
          <p class="mt-2 text-lg text-gray-500">
            Créez et gérez facilement tous vos projets immobiliers en un seul endroit.
          </p>
        </div>
      </div>
    </div>
  </div>
</div>

</div>
{% endblock %}
