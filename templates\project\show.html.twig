{% extends 'base.html.twig' %}

{% block title %}{{ project.name }} - OptiImmo{% endblock %}

{% block body %}
<div class="min-h-screen bg-gray-50 py-8">
    <div class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
        <!-- Header -->
        <div class="md:flex md:items-center md:justify-between mb-8">
            <div class="flex-1 min-w-0">
                <nav class="flex" aria-label="Breadcrumb">
                    <ol class="flex items-center space-x-4">
                        <li>
                            <a href="{{ path('app_project_index') }}" class="text-gray-400 hover:text-gray-500">
                                <svg class="flex-shrink-0 h-5 w-5" xmlns="http://www.w3.org/2000/svg" viewBox="0 0 20 20" fill="currentColor">
                                    <path d="M10.707 2.293a1 1 0 00-1.414 0l-7 7a1 1 0 001.414 1.414L4 10.414V17a1 1 0 001 1h2a1 1 0 001-1v-2a1 1 0 011-1h2a1 1 0 011 1v2a1 1 0 001 1h2a1 1 0 001-1v-6.586l.293.293a1 1 0 001.414-1.414l-7-7z" />
                                </svg>
                                <span class="sr-only">Accueil</span>
                            </a>
                        </li>
                        <li>
                            <div class="flex items-center">
                                <svg class="flex-shrink-0 h-5 w-5 text-gray-300" xmlns="http://www.w3.org/2000/svg" fill="currentColor" viewBox="0 0 20 20">
                                    <path d="M5.555 17.776l8-16 .894.448-8 16-.894-.448z" />
                                </svg>
                                <a href="{{ path('app_project_index') }}" class="ml-4 text-sm font-medium text-gray-500 hover:text-gray-700">Projets</a>
                            </div>
                        </li>
                        <li>
                            <div class="flex items-center">
                                <svg class="flex-shrink-0 h-5 w-5 text-gray-300" xmlns="http://www.w3.org/2000/svg" fill="currentColor" viewBox="0 0 20 20">
                                    <path d="M5.555 17.776l8-16 .894.448-8 16-.894-.448z" />
                                </svg>
                                <span class="ml-4 text-sm font-medium text-gray-500">{{ project.name }}</span>
                            </div>
                        </li>
                    </ol>
                </nav>
                <h1 class="mt-2 text-2xl font-bold leading-7 text-gray-900 sm:text-3xl sm:truncate"
                    data-controller="inline-edit"
                    data-inline-edit-project-id-value="{{ project.id }}"
                    data-inline-edit-field-value="name"
                    data-inline-edit-target="field"
                    data-action="click->inline-edit#edit">
                    {{ project.name }}
                </h1>
                <p class="mt-1 text-sm text-gray-500">
                    Créé le {{ project.createdAt|date('d/m/Y à H:i') }}
                </p>
            </div>
        </div>

        <!-- Project Details Grid -->
        <div class="grid grid-cols-1 lg:grid-cols-3 gap-8">
            <!-- Main Content -->
            <div class="lg:col-span-2 space-y-8">
                <!-- Project Information Card -->
                <div class="bg-white shadow rounded-lg">
                    <div class="px-6 py-4 border-b border-gray-200">
                        <h3 class="text-lg font-medium text-gray-900">Informations du projet</h3>
                    </div>
                    <div class="px-6 py-4">
                        <div class="grid grid-cols-1 md:grid-cols-2 gap-6">
                            <div>
                                <label class="block text-sm font-medium text-gray-700">Montant du loyer</label>
                                <div class="mt-1 text-sm text-gray-900"
                                     data-controller="inline-edit"
                                     data-inline-edit-project-id-value="{{ project.id }}"
                                     data-inline-edit-field-value="rentAmount"
                                     data-inline-edit-type-value="number"
                                     data-inline-edit-suffix-value=" €"
                                     data-inline-edit-target="field"
                                     data-action="click->inline-edit#edit">
                                    {{ project.rentAmount ? (project.rentAmount|number_format(2, ',', ' ') ~ ' €') : 'Non défini' }}
                                </div>
                            </div>
                            <div>
                                <label class="block text-sm font-medium text-gray-700">Taux de vacance</label>
                                <div class="mt-1 text-sm text-gray-900"
                                     data-controller="inline-edit"
                                     data-inline-edit-project-id-value="{{ project.id }}"
                                     data-inline-edit-field-value="vacancyRate"
                                     data-inline-edit-type-value="number"
                                     data-inline-edit-suffix-value=" %"
                                     data-inline-edit-target="field"
                                     data-action="click->inline-edit#edit">
                                    {{ project.vacancyRate ? (project.vacancyRate ~ ' %') : 'Non défini' }}
                                </div>
                            </div>
                            <div>
                                <label class="block text-sm font-medium text-gray-700">Prix du terrain</label>
                                <div class="mt-1 text-sm text-gray-900"
                                     data-controller="inline-edit"
                                     data-inline-edit-project-id-value="{{ project.id }}"
                                     data-inline-edit-field-value="landPrice"
                                     data-inline-edit-type-value="number"
                                     data-inline-edit-suffix-value=" €"
                                     data-inline-edit-target="field"
                                     data-action="click->inline-edit#edit">
                                    {{ project.landPrice ? (project.landPrice|number_format(2, ',', ' ') ~ ' €') : 'Non défini' }}
                                </div>
                            </div>
                            <div>
                                <label class="block text-sm font-medium text-gray-700">Valeur du bien</label>
                                <div class="mt-1 text-sm text-gray-900"
                                     data-controller="inline-edit"
                                     data-inline-edit-project-id-value="{{ project.id }}"
                                     data-inline-edit-field-value="propertyValue"
                                     data-inline-edit-type-value="number"
                                     data-inline-edit-suffix-value=" €"
                                     data-inline-edit-target="field"
                                     data-action="click->inline-edit#edit">
                                    {{ project.propertyValue ? (project.propertyValue|number_format(2, ',', ' ') ~ ' €') : 'Non défini' }}
                                </div>
                            </div>
                            <div>
                                <label class="block text-sm font-medium text-gray-700">Durée d'amortissement</label>
                                <div class="mt-1 text-sm text-gray-900"
                                     data-controller="inline-edit"
                                     data-inline-edit-project-id-value="{{ project.id }}"
                                     data-inline-edit-field-value="amortizationDuration"
                                     data-inline-edit-type-value="number"
                                     data-inline-edit-suffix-value=" ans"
                                     data-inline-edit-target="field"
                                     data-action="click->inline-edit#edit">
                                    {{ project.amortizationDuration ? (project.amortizationDuration ~ ' ans') : 'Non défini' }}
                                </div>
                            </div>
                        </div>
                    </div>
                </div>

                <!-- Loans Section -->
                <div class="bg-white shadow rounded-lg">
                    <div class="px-6 py-4 border-b border-gray-200 flex justify-between items-center">
                        <h3 class="text-lg font-medium text-gray-900">Prêts ({{ loans|length }})</h3>
                        <button onclick="openLoanModal()"
                                class="inline-flex items-center px-3 py-2 border border-transparent text-sm leading-4 font-medium rounded-md text-white bg-indigo-600 hover:bg-indigo-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-indigo-500">
                            <svg class="-ml-0.5 mr-2 h-4 w-4" xmlns="http://www.w3.org/2000/svg" viewBox="0 0 20 20" fill="currentColor">
                                <path fill-rule="evenodd" d="M10 3a1 1 0 011 1v5h5a1 1 0 110 2h-5v5a1 1 0 11-2 0v-5H4a1 1 0 110-2h5V4a1 1 0 011-1z" clip-rule="evenodd" />
                            </svg>
                            Ajouter un prêt
                        </button>
                    </div>
                    <div class="px-6 py-4">
                        {% if loans|length > 0 %}
                            <div id="loans-container" class="space-y-4">
                                {% for loan in loans %}
                                    {% set loanStats = loansStats.loans[loop.index0] %}
                                    <div class="border border-gray-200 rounded-lg p-4 loan-item" data-loan-id="{{ loan.id }}">
                                        <div class="flex justify-between items-start mb-3">
                                            <div class="grid grid-cols-2 md:grid-cols-4 gap-4 flex-1">
                                                <div>
                                                    <span class="text-sm font-medium text-gray-500">Montant</span>
                                                    <p class="text-sm text-gray-900">{{ loan.amount|number_format(2, ',', ' ') }} €</p>
                                                </div>
                                                <div>
                                                    <span class="text-sm font-medium text-gray-500">Taux d'intérêt</span>
                                                    <p class="text-sm text-gray-900">{{ loan.interestRate }} %</p>
                                                </div>
                                                <div>
                                                    <span class="text-sm font-medium text-gray-500">Durée</span>
                                                    <p class="text-sm text-gray-900">{{ loan.durationYears }} ans</p>
                                                </div>
                                                <div>
                                                    <span class="text-sm font-medium text-gray-500">Mensualité</span>
                                                    <p class="text-sm text-gray-900 font-semibold text-indigo-600">{{ loanStats.monthlyPayment|number_format(2, ',', ' ') }} €</p>
                                                </div>
                                            </div>
                                            <button onclick="deleteLoan({{ loan.id }})"
                                                    class="ml-4 text-red-600 hover:text-red-800">
                                                <svg class="h-4 w-4" xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                                                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M19 7l-.867 12.142A2 2 0 0116.138 21H7.862a2 2 0 01-1.995-1.858L5 7m5 4v6m4-6v6m1-10V4a1 1 0 00-1-1h-4a1 1 0 00-1 1v3M4 7h16" />
                                                </svg>
                                            </button>
                                        </div>
                                        <div class="grid grid-cols-2 md:grid-cols-3 gap-4 text-xs text-gray-600 bg-gray-50 p-3 rounded">
                                            <div>
                                                <span class="font-medium">Coût total:</span>
                                                <span class="block">{{ loanStats.totalCost|number_format(2, ',', ' ') }} €</span>
                                            </div>
                                            <div>
                                                <span class="font-medium">Intérêts totaux:</span>
                                                <span class="block">{{ loanStats.totalInterest|number_format(2, ',', ' ') }} €</span>
                                            </div>
                                            <div>
                                                <span class="font-medium">Assurance mensuelle:</span>
                                                <span class="block">{{ loanStats.monthlyInsurance|number_format(2, ',', ' ') }} €</span>
                                            </div>
                                        </div>
                                    </div>
                                {% endfor %}
                            </div>

                            <!-- Loans Summary -->
                            {% if loansStats %}
                                <div class="mt-6 bg-indigo-50 border border-indigo-200 rounded-lg p-4">
                                    <h4 class="text-sm font-medium text-indigo-900 mb-3">Résumé des prêts</h4>
                                    <div class="grid grid-cols-2 md:grid-cols-4 gap-4 text-sm">
                                        <div>
                                            <span class="text-indigo-600 font-medium">Total emprunté:</span>
                                            <p class="text-indigo-900 font-semibold">{{ loansStats.summary.totalPrincipal|number_format(2, ',', ' ') }} €</p>
                                        </div>
                                        <div>
                                            <span class="text-indigo-600 font-medium">Mensualités totales:</span>
                                            <p class="text-indigo-900 font-semibold">{{ loansStats.summary.totalMonthlyPayment|number_format(2, ',', ' ') }} €</p>
                                        </div>
                                        <div>
                                            <span class="text-indigo-600 font-medium">Coût total:</span>
                                            <p class="text-indigo-900 font-semibold">{{ loansStats.summary.totalCost|number_format(2, ',', ' ') }} €</p>
                                        </div>
                                        <div>
                                            <span class="text-indigo-600 font-medium">Intérêts totaux:</span>
                                            <p class="text-indigo-900 font-semibold">{{ loansStats.summary.totalInterest|number_format(2, ',', ' ') }} €</p>
                                        </div>
                                    </div>
                                </div>
                            {% endif %}
                        {% else %}
                            <div class="text-center py-8">
                                <svg class="mx-auto h-12 w-12 text-gray-400" xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 8c-1.657 0-3 .895-3 2s1.343 2 3 2 3 .895 3 2-1.343 2-3 2m0-8c1.11 0 2.08.402 2.599 1M12 8V7m0 1v8m0 0v1m0-1c-1.11 0-2.08-.402-2.599-1" />
                                </svg>
                                <h3 class="mt-2 text-sm font-medium text-gray-900">Aucun prêt</h3>
                                <p class="mt-1 text-sm text-gray-500">Commencez par ajouter un prêt pour ce projet.</p>
                                <div class="mt-6">
                                    <button onclick="openLoanModal()"
                                            class="inline-flex items-center px-4 py-2 border border-transparent shadow-sm text-sm font-medium rounded-md text-white bg-indigo-600 hover:bg-indigo-700">
                                        <svg class="-ml-1 mr-2 h-5 w-5" xmlns="http://www.w3.org/2000/svg" viewBox="0 0 20 20" fill="currentColor">
                                            <path fill-rule="evenodd" d="M10 3a1 1 0 011 1v5h5a1 1 0 110 2h-5v5a1 1 0 11-2 0v-5H4a1 1 0 110-2h5V4a1 1 0 011-1z" clip-rule="evenodd" />
                                        </svg>
                                        Ajouter un prêt
                                    </button>
                                </div>
                            </div>
                        {% endif %}
                    </div>
                </div>
            </div>

            <!-- Sidebar -->
            <div class="space-y-8">
                <!-- Profitability Dashboard -->
                <div class="bg-white shadow rounded-lg">
                    <div class="px-6 py-4 border-b border-gray-200">
                        <h3 class="text-lg font-medium text-gray-900">Rentabilité</h3>
                    </div>
                    <div class="px-6 py-4">
                        <!-- Profitability Score -->
                        <div class="mb-6">
                            <div class="flex items-center justify-between mb-2">
                                <span class="text-sm font-medium text-gray-700">Score de rentabilité</span>
                                <span class="text-sm font-semibold text-gray-900">{{ profitabilityAnalysis.score.profitabilityScore|number_format(0) }}/100</span>
                            </div>
                            <div class="w-full bg-gray-200 rounded-full h-2">
                                <div class="bg-gradient-to-r from-red-500 via-yellow-500 to-green-500 h-2 rounded-full"
                                     style="width: {{ profitabilityAnalysis.score.profitabilityScore }}%"></div>
                            </div>
                            <div class="mt-1 text-xs text-center">
                                <span class="px-2 py-1 rounded-full text-xs font-medium
                                    {% if profitabilityAnalysis.score.profitabilityScore >= 80 %}bg-green-100 text-green-800
                                    {% elseif profitabilityAnalysis.score.profitabilityScore >= 60 %}bg-blue-100 text-blue-800
                                    {% elseif profitabilityAnalysis.score.profitabilityScore >= 40 %}bg-yellow-100 text-yellow-800
                                    {% elseif profitabilityAnalysis.score.profitabilityScore >= 20 %}bg-orange-100 text-orange-800
                                    {% else %}bg-red-100 text-red-800{% endif %}">
                                    {{ profitabilityAnalysis.score.rating }}
                                </span>
                            </div>
                        </div>

                        <!-- Key Metrics -->
                        <div class="space-y-4">
                            <div class="grid grid-cols-1 gap-3">
                                <div class="bg-gray-50 p-3 rounded">
                                    <div class="text-xs font-medium text-gray-500 uppercase tracking-wide">Cash-flow mensuel</div>
                                    <div class="mt-1 text-lg font-semibold
                                        {% if profitabilityAnalysis.cashFlow.cashFlow.monthly >= 0 %}text-green-600
                                        {% else %}text-red-600{% endif %}">
                                        {{ profitabilityAnalysis.cashFlow.cashFlow.monthly|number_format(2, ',', ' ') }} €
                                    </div>
                                </div>

                                <div class="bg-gray-50 p-3 rounded">
                                    <div class="text-xs font-medium text-gray-500 uppercase tracking-wide">Rendement brut</div>
                                    <div class="mt-1 text-lg font-semibold text-indigo-600">
                                        {{ profitabilityAnalysis.returns.grossYield|number_format(2) }} %
                                    </div>
                                </div>

                                <div class="bg-gray-50 p-3 rounded">
                                    <div class="text-xs font-medium text-gray-500 uppercase tracking-wide">ROI</div>
                                    <div class="mt-1 text-lg font-semibold
                                        {% if profitabilityAnalysis.returns.roi >= 0 %}text-green-600
                                        {% else %}text-red-600{% endif %}">
                                        {{ profitabilityAnalysis.returns.roi|number_format(2) }} %
                                    </div>
                                </div>
                            </div>

                            <!-- Break-even Analysis -->
                            <div class="border-t pt-4">
                                <h4 class="text-sm font-medium text-gray-900 mb-2">Seuil de rentabilité</h4>
                                <div class="text-sm">
                                    {% if profitabilityAnalysis.breakEven.isBreakEven %}
                                        <div class="flex items-center text-green-600">
                                            <svg class="h-4 w-4 mr-1" fill="currentColor" viewBox="0 0 20 20">
                                                <path fill-rule="evenodd" d="M10 18a8 8 0 100-16 8 8 0 000 16zm3.707-9.293a1 1 0 00-1.414-1.414L9 10.586 7.707 9.293a1 1 0 00-1.414 1.414l2 2a1 1 0 001.414 0l4-4z" clip-rule="evenodd"/>
                                            </svg>
                                            Rentable
                                        </div>
                                        <div class="text-xs text-gray-500 mt-1">
                                            Excédent: {{ (profitabilityAnalysis.breakEven.rentGap * -1)|number_format(2, ',', ' ') }} €/mois
                                        </div>
                                    {% else %}
                                        <div class="flex items-center text-red-600">
                                            <svg class="h-4 w-4 mr-1" fill="currentColor" viewBox="0 0 20 20">
                                                <path fill-rule="evenodd" d="M10 18a8 8 0 100-16 8 8 0 000 16zM8.707 7.293a1 1 0 00-1.414 1.414L8.586 10l-1.293 1.293a1 1 0 101.414 1.414L10 11.414l1.293 1.293a1 1 0 001.414-1.414L11.414 10l1.293-1.293a1 1 0 00-1.414-1.414L10 8.586 8.707 7.293z" clip-rule="evenodd"/>
                                            </svg>
                                            Non rentable
                                        </div>
                                        <div class="text-xs text-gray-500 mt-1">
                                            Manque: {{ profitabilityAnalysis.breakEven.rentGap|number_format(2, ',', ' ') }} €/mois
                                        </div>
                                    {% endif %}
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
                <!-- Initial Charges -->
                <div class="bg-white shadow rounded-lg">
                    <div class="px-6 py-4 border-b border-gray-200 flex justify-between items-center">
                        <h3 class="text-lg font-medium text-gray-900">Charges initiales</h3>
                        <button onclick="openChargeModal('initial')"
                                class="inline-flex items-center px-2 py-1 border border-transparent text-xs font-medium rounded text-indigo-700 bg-indigo-100 hover:bg-indigo-200">
                            <svg class="-ml-0.5 mr-1 h-3 w-3" xmlns="http://www.w3.org/2000/svg" viewBox="0 0 20 20" fill="currentColor">
                                <path fill-rule="evenodd" d="M10 3a1 1 0 011 1v5h5a1 1 0 110 2h-5v5a1 1 0 11-2 0v-5H4a1 1 0 110-2h5V4a1 1 0 011-1z" clip-rule="evenodd" />
                            </svg>
                            Ajouter
                        </button>
                    </div>
                    <div class="px-6 py-4">
                        {% if initialCharges|length > 0 %}
                            <div id="initial-charges-container" class="space-y-3">
                                {% for charge in initialCharges %}
                                    <div class="flex justify-between items-center charge-item" data-charge-id="{{ charge.id }}">
                                        <div class="flex-1">
                                            <p class="text-sm font-medium text-gray-900">{{ charge.label }}</p>
                                            <p class="text-sm text-gray-500">{{ charge.amount|number_format(2, ',', ' ') }} €</p>
                                            {% if charge.description %}
                                                <p class="text-xs text-gray-400 mt-1">{{ charge.description }}</p>
                                            {% endif %}
                                        </div>
                                        <button onclick="deleteCharge({{ charge.id }})"
                                                class="text-red-600 hover:text-red-800">
                                            <svg class="h-4 w-4" xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M6 18L18 6M6 6l12 12" />
                                            </svg>
                                        </button>
                                    </div>
                                {% endfor %}
                            </div>

                            <!-- Initial Charges Summary -->
                            <div class="mt-4 bg-gray-50 border border-gray-200 rounded p-3">
                                <div class="text-sm">
                                    <span class="font-medium text-gray-700">Total des charges initiales:</span>
                                    <span class="font-semibold text-gray-900">{{ expenseStats.breakdown.initial.total|number_format(2, ',', ' ') }} €</span>
                                </div>
                            </div>
                        {% else %}
                            <p class="text-sm text-gray-500 text-center py-4">Aucune charge initiale</p>
                        {% endif %}
                    </div>
                </div>

                <!-- Recurring Charges -->
                <div class="bg-white shadow rounded-lg">
                    <div class="px-6 py-4 border-b border-gray-200 flex justify-between items-center">
                        <h3 class="text-lg font-medium text-gray-900">Charges récurrentes</h3>
                        <button onclick="openChargeModal('recurring')"
                                class="inline-flex items-center px-2 py-1 border border-transparent text-xs font-medium rounded text-indigo-700 bg-indigo-100 hover:bg-indigo-200">
                            <svg class="-ml-0.5 mr-1 h-3 w-3" xmlns="http://www.w3.org/2000/svg" viewBox="0 0 20 20" fill="currentColor">
                                <path fill-rule="evenodd" d="M10 3a1 1 0 011 1v5h5a1 1 0 110 2h-5v5a1 1 0 11-2 0v-5H4a1 1 0 110-2h5V4a1 1 0 011-1z" clip-rule="evenodd" />
                            </svg>
                            Ajouter
                        </button>
                    </div>
                    <div class="px-6 py-4">
                        {% if recurringCharges|length > 0 %}
                            <div id="recurring-charges-container" class="space-y-3">
                                {% for charge in recurringCharges %}
                                    {% set chargeData = expenseStats.breakdown.recurring.expenses[loop.index0] %}
                                    <div class="flex justify-between items-center charge-item" data-charge-id="{{ charge.id }}">
                                        <div class="flex-1">
                                            <p class="text-sm font-medium text-gray-900">{{ charge.label }}</p>
                                            <div class="text-sm text-gray-500">
                                                <span>{{ charge.amount|number_format(2, ',', ' ') }} €</span>
                                                {% if charge.frequency %}
                                                    <span class="text-xs">({{ charge.frequency }})</span>
                                                {% endif %}
                                            </div>
                                            <div class="text-xs text-indigo-600 mt-1">
                                                Mensuel: {{ chargeData.monthlyAmount|number_format(2, ',', ' ') }} € •
                                                Annuel: {{ chargeData.yearlyAmount|number_format(2, ',', ' ') }} €
                                            </div>
                                            {% if charge.description %}
                                                <p class="text-xs text-gray-400 mt-1">{{ charge.description }}</p>
                                            {% endif %}
                                        </div>
                                        <button onclick="deleteCharge({{ charge.id }})"
                                                class="text-red-600 hover:text-red-800">
                                            <svg class="h-4 w-4" xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M6 18L18 6M6 6l12 12" />
                                            </svg>
                                        </button>
                                    </div>
                                {% endfor %}
                            </div>

                            <!-- Recurring Charges Summary -->
                            <div class="mt-4 bg-green-50 border border-green-200 rounded p-3">
                                <div class="text-sm space-y-1">
                                    <div>
                                        <span class="font-medium text-green-700">Total mensuel:</span>
                                        <span class="font-semibold text-green-900">{{ expenseStats.breakdown.recurring.totalMonthly|number_format(2, ',', ' ') }} €</span>
                                    </div>
                                    <div>
                                        <span class="font-medium text-green-700">Total annuel:</span>
                                        <span class="font-semibold text-green-900">{{ expenseStats.breakdown.recurring.totalYearly|number_format(2, ',', ' ') }} €</span>
                                    </div>
                                </div>
                            </div>
                        {% else %}
                            <p class="text-sm text-gray-500 text-center py-4">Aucune charge récurrente</p>
                        {% endif %}
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

<!-- Loan Modal -->
<div id="loanModal" class="fixed inset-0 bg-gray-600 bg-opacity-50 overflow-y-auto h-full w-full hidden z-50">
    <div class="relative top-20 mx-auto p-5 border w-full max-w-md shadow-lg rounded-md bg-white">
        <div class="mt-3">
            <h3 class="text-lg font-medium text-gray-900 text-center">Ajouter un prêt</h3>
            <form id="loanForm" class="mt-4 space-y-4">
                <div>
                    <label for="loanAmount" class="block text-sm font-medium text-gray-700">Montant (€)</label>
                    <input type="number" id="loanAmount" name="amount" step="0.01" required
                           class="mt-1 block w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-indigo-500 focus:border-indigo-500">
                </div>
                <div class="grid grid-cols-2 gap-4">
                    <div>
                        <label for="loanInterestRate" class="block text-sm font-medium text-gray-700">Taux d'intérêt (%)</label>
                        <input type="number" id="loanInterestRate" name="interestRate" step="0.01" required
                               class="mt-1 block w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-indigo-500 focus:border-indigo-500">
                    </div>
                    <div>
                        <label for="loanInsuranceRate" class="block text-sm font-medium text-gray-700">Taux d'assurance (%)</label>
                        <input type="number" id="loanInsuranceRate" name="insuranceRate" step="0.01" required
                               class="mt-1 block w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-indigo-500 focus:border-indigo-500">
                    </div>
                </div>
                <div class="grid grid-cols-2 gap-4">
                    <div>
                        <label for="loanDuration" class="block text-sm font-medium text-gray-700">Durée (années)</label>
                        <input type="number" id="loanDuration" name="durationYears" required
                               class="mt-1 block w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-indigo-500 focus:border-indigo-500">
                    </div>
                    <div>
                        <label for="loanDeferred" class="block text-sm font-medium text-gray-700">Différé (mois)</label>
                        <input type="number" id="loanDeferred" name="deferredMonths" value="0"
                               class="mt-1 block w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-indigo-500 focus:border-indigo-500">
                    </div>
                </div>
                <div>
                    <label for="loanStartDate" class="block text-sm font-medium text-gray-700">Date de début</label>
                    <input type="date" id="loanStartDate" name="startDate" required
                           class="mt-1 block w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-indigo-500 focus:border-indigo-500">
                </div>
                <div class="flex items-center justify-end space-x-3 mt-6">
                    <button type="button" onclick="closeLoanModal()"
                            class="px-4 py-2 bg-gray-300 text-gray-700 rounded-md hover:bg-gray-400 transition-colors">
                        Annuler
                    </button>
                    <button type="submit"
                            class="px-4 py-2 bg-indigo-600 text-white rounded-md hover:bg-indigo-700 transition-colors">
                        Ajouter
                    </button>
                </div>
            </form>
        </div>
    </div>
</div>

<!-- Charge Modal -->
<div id="chargeModal" class="fixed inset-0 bg-gray-600 bg-opacity-50 overflow-y-auto h-full w-full hidden z-50">
    <div class="relative top-20 mx-auto p-5 border w-full max-w-md shadow-lg rounded-md bg-white">
        <div class="mt-3">
            <h3 id="chargeModalTitle" class="text-lg font-medium text-gray-900 text-center">Ajouter une charge</h3>
            <form id="chargeForm" class="mt-4 space-y-4">
                <input type="hidden" id="chargeType" name="type">
                <div>
                    <label for="chargeLabel" class="block text-sm font-medium text-gray-700">Libellé</label>
                    <input type="text" id="chargeLabel" name="label" required
                           class="mt-1 block w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-indigo-500 focus:border-indigo-500">
                </div>
                <div>
                    <label for="chargeAmount" class="block text-sm font-medium text-gray-700">Montant (€)</label>
                    <input type="number" id="chargeAmount" name="amount" step="0.01" required
                           class="mt-1 block w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-indigo-500 focus:border-indigo-500">
                </div>
                <div id="frequencyField" class="hidden">
                    <label for="chargeFrequency" class="block text-sm font-medium text-gray-700">Fréquence</label>
                    <select id="chargeFrequency" name="frequency"
                            class="mt-1 block w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-indigo-500 focus:border-indigo-500">
                        <option value="">Sélectionner une fréquence</option>
                        <option value="weekly">Hebdomadaire</option>
                        <option value="monthly">Mensuelle</option>
                        <option value="quarterly">Trimestrielle</option>
                        <option value="yearly">Annuelle</option>
                    </select>
                </div>
                <div>
                    <label for="chargeStartDate" class="block text-sm font-medium text-gray-700">Date de début</label>
                    <input type="date" id="chargeStartDate" name="startDate" required
                           class="mt-1 block w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-indigo-500 focus:border-indigo-500">
                </div>
                <div>
                    <label for="chargeDescription" class="block text-sm font-medium text-gray-700">Description (optionnel)</label>
                    <textarea id="chargeDescription" name="description" rows="3"
                              class="mt-1 block w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-indigo-500 focus:border-indigo-500"></textarea>
                </div>
                <div class="flex items-center justify-end space-x-3 mt-6">
                    <button type="button" onclick="closeChargeModal()"
                            class="px-4 py-2 bg-gray-300 text-gray-700 rounded-md hover:bg-gray-400 transition-colors">
                        Annuler
                    </button>
                    <button type="submit"
                            class="px-4 py-2 bg-indigo-600 text-white rounded-md hover:bg-indigo-700 transition-colors">
                        Ajouter
                    </button>
                </div>
            </form>
        </div>
    </div>
</div>


<script>
const projectId = {{ project.id }};

// Loan modal functions
function openLoanModal() {
    document.getElementById('loanModal').classList.remove('hidden');
    document.getElementById('loanStartDate').value = new Date().toISOString().split('T')[0];
}

function closeLoanModal() {
    document.getElementById('loanModal').classList.add('hidden');
    document.getElementById('loanForm').reset();
}

document.getElementById('loanForm').addEventListener('submit', function(e) {
    e.preventDefault();

    const formData = new FormData(this);

    fetch(`/projet/${projectId}/loan/add`, {
        method: 'POST',
        body: formData,
        headers: {
            'X-Requested-With': 'XMLHttpRequest'
        }
    })
    .then(response => response.json())
    .then(data => {
        if (data.success) {
            closeLoanModal();
            showNotification(data.message || 'Prêt ajouté avec succès', 'success');
            setTimeout(() => location.reload(), 500);
        } else {
            showNotification(data.message || 'Erreur lors de l\'ajout du prêt', 'error');
        }
    })
    .catch(error => {
        console.error('Error:', error);
        showNotification('Une erreur est survenue', 'error');
    });
});

// Charge modal functions
function openChargeModal(type) {
    const modal = document.getElementById('chargeModal');
    const title = document.getElementById('chargeModalTitle');
    const typeInput = document.getElementById('chargeType');
    const frequencyField = document.getElementById('frequencyField');

    typeInput.value = type;

    if (type === 'initial') {
        title.textContent = 'Ajouter une charge initiale';
        frequencyField.classList.add('hidden');
        document.getElementById('chargeFrequency').required = false;
    } else {
        title.textContent = 'Ajouter une charge récurrente';
        frequencyField.classList.remove('hidden');
        document.getElementById('chargeFrequency').required = true;
    }

    document.getElementById('chargeStartDate').value = new Date().toISOString().split('T')[0];
    modal.classList.remove('hidden');
}

function closeChargeModal() {
    document.getElementById('chargeModal').classList.add('hidden');
    document.getElementById('chargeForm').reset();
}

document.getElementById('chargeForm').addEventListener('submit', function(e) {
    e.preventDefault();

    const formData = new FormData(this);

    fetch(`/projet/${projectId}/charge/add`, {
        method: 'POST',
        body: formData,
        headers: {
            'X-Requested-With': 'XMLHttpRequest'
        }
    })
    .then(response => response.json())
    .then(data => {
        if (data.success) {
            closeChargeModal();
            showNotification(data.message || 'Charge ajoutée avec succès', 'success');
            setTimeout(() => location.reload(), 500);
        } else {
            showNotification(data.message || 'Erreur lors de l\'ajout de la charge', 'error');
        }
    })
    .catch(error => {
        console.error('Error:', error);
        showNotification('Une erreur est survenue', 'error');
    });
});

// Delete functions
function deleteLoan(loanId) {
    if (confirm('Êtes-vous sûr de vouloir supprimer ce prêt ?')) {
        fetch(`/projet/${projectId}/loan/${loanId}/delete`, {
            method: 'DELETE',
            headers: {
                'X-Requested-With': 'XMLHttpRequest'
            }
        })
        .then(response => response.json())
        .then(data => {
            if (data.success) {
                showNotification(data.message || 'Prêt supprimé avec succès', 'success');
                setTimeout(() => location.reload(), 500);
            } else {
                showNotification(data.message || 'Erreur lors de la suppression', 'error');
            }
        })
        .catch(error => {
            console.error('Error:', error);
            showNotification('Une erreur est survenue', 'error');
        });
    }
}

function deleteCharge(chargeId) {
    if (confirm('Êtes-vous sûr de vouloir supprimer cette charge ?')) {
        fetch(`/projet/${projectId}/charge/${chargeId}/delete`, {
            method: 'DELETE',
            headers: {
                'X-Requested-With': 'XMLHttpRequest'
            }
        })
        .then(response => response.json())
        .then(data => {
            if (data.success) {
                showNotification(data.message || 'Charge supprimée avec succès', 'success');
                setTimeout(() => location.reload(), 500);
            } else {
                showNotification(data.message || 'Erreur lors de la suppression', 'error');
            }
        })
        .catch(error => {
            console.error('Error:', error);
            showNotification('Une erreur est survenue', 'error');
        });
    }
}

// Notification function
function showNotification(message, type = 'info') {
    const notification = document.createElement('div');
    notification.className = `fixed top-4 right-4 z-50 p-4 rounded-md shadow-lg transition-all duration-300 ${
        type === 'success' ? 'bg-green-500 text-white' :
        type === 'error' ? 'bg-red-500 text-white' :
        'bg-blue-500 text-white'
    }`;
    notification.textContent = message;

    document.body.appendChild(notification);

    setTimeout(() => {
        notification.style.opacity = '0';
        setTimeout(() => {
            if (notification.parentNode) {
                notification.parentNode.removeChild(notification);
            }
        }, 300);
    }, 4000);

    notification.addEventListener('click', () => {
        notification.style.opacity = '0';
        setTimeout(() => {
            if (notification.parentNode) {
                notification.parentNode.removeChild(notification);
            }
        }, 300);
    });
}

// Close modals when clicking outside
document.getElementById('loanModal').addEventListener('click', function(e) {
    if (e.target === this) closeLoanModal();
});

document.getElementById('chargeModal').addEventListener('click', function(e) {
    if (e.target === this) closeChargeModal();
});
</script>

{% endblock %}
