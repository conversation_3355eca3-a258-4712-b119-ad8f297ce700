{% extends 'base.html.twig' %}

{% block title %}{{ project.name }} - OptiImmo{% endblock %}

{% block body %}
<div class="min-h-screen bg-gray-50 py-8">
    <div class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
        <!-- Header -->
        <div class="md:flex md:items-center md:justify-between mb-8">
            <div class="flex-1 min-w-0">
                <nav class="flex" aria-label="Breadcrumb">
                    <ol class="flex items-center space-x-4">
                        <li>
                            <a href="{{ path('app_project_index') }}" class="text-gray-400 hover:text-gray-500">
                                <svg class="flex-shrink-0 h-5 w-5" xmlns="http://www.w3.org/2000/svg" viewBox="0 0 20 20" fill="currentColor">
                                    <path d="M10.707 2.293a1 1 0 00-1.414 0l-7 7a1 1 0 001.414 1.414L4 10.414V17a1 1 0 001 1h2a1 1 0 001-1v-2a1 1 0 011-1h2a1 1 0 011 1v2a1 1 0 001 1h2a1 1 0 001-1v-6.586l.293.293a1 1 0 001.414-1.414l-7-7z" />
                                </svg>
                                <span class="sr-only">Accueil</span>
                            </a>
                        </li>
                        <li>
                            <div class="flex items-center">
                                <svg class="flex-shrink-0 h-5 w-5 text-gray-300" xmlns="http://www.w3.org/2000/svg" fill="currentColor" viewBox="0 0 20 20">
                                    <path d="M5.555 17.776l8-16 .894.448-8 16-.894-.448z" />
                                </svg>
                                <a href="{{ path('app_project_index') }}" class="ml-4 text-sm font-medium text-gray-500 hover:text-gray-700">Projets</a>
                            </div>
                        </li>
                        <li>
                            <div class="flex items-center">
                                <svg class="flex-shrink-0 h-5 w-5 text-gray-300" xmlns="http://www.w3.org/2000/svg" fill="currentColor" viewBox="0 0 20 20">
                                    <path d="M5.555 17.776l8-16 .894.448-8 16-.894-.448z" />
                                </svg>
                                <span class="ml-4 text-sm font-medium text-gray-500">{{ project.name }}</span>
                            </div>
                        </li>
                    </ol>
                </nav>
                <h1 class="mt-2 text-2xl font-bold leading-7 text-gray-900 sm:text-3xl sm:truncate"
                    data-controller="inline-edit"
                    data-inline-edit-project-id-value="{{ project.id }}"
                    data-inline-edit-field-value="name"
                    data-inline-edit-target="field"
                    data-action="click->inline-edit#edit">
                    {{ project.name }}
                </h1>
                <p class="mt-1 text-sm text-gray-500">
                    Créé le {{ project.createdAt|date('d/m/Y à H:i') }}
                </p>
            </div>
        </div>

        <!-- Project Details Grid -->
        <div class="grid grid-cols-1 lg:grid-cols-3 gap-8">
            <!-- Main Content -->
            <div class="lg:col-span-2 space-y-8">
                <!-- Project Information Card -->
                <div class="bg-white shadow rounded-lg">
                    <div class="px-6 py-4 border-b border-gray-200">
                        <h3 class="text-lg font-medium text-gray-900">Informations du projet</h3>
                    </div>
                    <div class="px-6 py-4">
                        <div class="grid grid-cols-1 md:grid-cols-2 gap-6">
                            <div>
                                <label class="block text-sm font-medium text-gray-700">Montant du loyer</label>
                                <div class="mt-1 text-sm text-gray-900"
                                     data-controller="inline-edit"
                                     data-inline-edit-project-id-value="{{ project.id }}"
                                     data-inline-edit-field-value="rentAmount"
                                     data-inline-edit-type-value="number"
                                     data-inline-edit-suffix-value=" €"
                                     data-inline-edit-target="field"
                                     data-action="click->inline-edit#edit">
                                    {{ project.rentAmount ? (project.rentAmount|number_format(2, ',', ' ') ~ ' €') : 'Non défini' }}
                                </div>
                            </div>
                            <div>
                                <label class="block text-sm font-medium text-gray-700">Taux de vacance</label>
                                <div class="mt-1 text-sm text-gray-900"
                                     data-controller="inline-edit"
                                     data-inline-edit-project-id-value="{{ project.id }}"
                                     data-inline-edit-field-value="vacancyRate"
                                     data-inline-edit-type-value="number"
                                     data-inline-edit-suffix-value=" %"
                                     data-inline-edit-target="field"
                                     data-action="click->inline-edit#edit">
                                    {{ project.vacancyRate ? (project.vacancyRate ~ ' %') : 'Non défini' }}
                                </div>
                            </div>
                            <div>
                                <label class="block text-sm font-medium text-gray-700">Prix du terrain</label>
                                <div class="mt-1 text-sm text-gray-900"
                                     data-controller="inline-edit"
                                     data-inline-edit-project-id-value="{{ project.id }}"
                                     data-inline-edit-field-value="landPrice"
                                     data-inline-edit-type-value="number"
                                     data-inline-edit-suffix-value=" €"
                                     data-inline-edit-target="field"
                                     data-action="click->inline-edit#edit">
                                    {{ project.landPrice ? (project.landPrice|number_format(2, ',', ' ') ~ ' €') : 'Non défini' }}
                                </div>
                            </div>
                            <div>
                                <label class="block text-sm font-medium text-gray-700">Valeur du bien</label>
                                <div class="mt-1 text-sm text-gray-900"
                                     data-controller="inline-edit"
                                     data-inline-edit-project-id-value="{{ project.id }}"
                                     data-inline-edit-field-value="propertyValue"
                                     data-inline-edit-type-value="number"
                                     data-inline-edit-suffix-value=" €"
                                     data-inline-edit-target="field"
                                     data-action="click->inline-edit#edit">
                                    {{ project.propertyValue ? (project.propertyValue|number_format(2, ',', ' ') ~ ' €') : 'Non défini' }}
                                </div>
                            </div>
                            <div>
                                <label class="block text-sm font-medium text-gray-700">Durée d'amortissement</label>
                                <div class="mt-1 text-sm text-gray-900"
                                     data-controller="inline-edit"
                                     data-inline-edit-project-id-value="{{ project.id }}"
                                     data-inline-edit-field-value="amortizationDuration"
                                     data-inline-edit-type-value="number"
                                     data-inline-edit-suffix-value=" ans"
                                     data-inline-edit-target="field"
                                     data-action="click->inline-edit#edit">
                                    {{ project.amortizationDuration ? (project.amortizationDuration ~ ' ans') : 'Non défini' }}
                                </div>
                            </div>
                        </div>
                    </div>
                </div>

                <!-- Loans Section -->
                <div class="bg-white shadow rounded-lg">
                    <div class="px-6 py-4 border-b border-gray-200 flex justify-between items-center">
                        <h3 class="text-lg font-medium text-gray-900">Prêts ({{ loans|length }})</h3>
                        <button data-controller="modal"
                                data-modal-project-id-value="{{ project.id }}"
                                data-action="click->modal#configure"
                                data-modal-type-param="loan"
                                class="inline-flex items-center px-3 py-2 border border-transparent text-sm leading-4 font-medium rounded-md text-white bg-indigo-600 hover:bg-indigo-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-indigo-500">
                            <svg class="-ml-0.5 mr-2 h-4 w-4" xmlns="http://www.w3.org/2000/svg" viewBox="0 0 20 20" fill="currentColor">
                                <path fill-rule="evenodd" d="M10 3a1 1 0 011 1v5h5a1 1 0 110 2h-5v5a1 1 0 11-2 0v-5H4a1 1 0 110-2h5V4a1 1 0 011-1z" clip-rule="evenodd" />
                            </svg>
                            Ajouter un prêt
                        </button>
                    </div>
                    <div class="px-6 py-4">
                        {% if loans|length > 0 %}
                            <div id="loans-container" class="space-y-4">
                                {% for loan in loans %}
                                    <div class="border border-gray-200 rounded-lg p-4 loan-item" data-loan-id="{{ loan.id }}">
                                        <div class="flex justify-between items-start">
                                            <div class="grid grid-cols-2 md:grid-cols-3 gap-4 flex-1">
                                                <div>
                                                    <span class="text-sm font-medium text-gray-500">Montant</span>
                                                    <p class="text-sm text-gray-900">{{ loan.amount|number_format(2, ',', ' ') }} €</p>
                                                </div>
                                                <div>
                                                    <span class="text-sm font-medium text-gray-500">Taux d'intérêt</span>
                                                    <p class="text-sm text-gray-900">{{ loan.interestRate }} %</p>
                                                </div>
                                                <div>
                                                    <span class="text-sm font-medium text-gray-500">Durée</span>
                                                    <p class="text-sm text-gray-900">{{ loan.durationYears }} ans</p>
                                                </div>
                                            </div>
                                            <button onclick="deleteLoan({{ loan.id }})" 
                                                    class="ml-4 text-red-600 hover:text-red-800">
                                                <svg class="h-4 w-4" xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                                                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M19 7l-.867 12.142A2 2 0 0116.138 21H7.862a2 2 0 01-1.995-1.858L5 7m5 4v6m4-6v6m1-10V4a1 1 0 00-1-1h-4a1 1 0 00-1 1v3M4 7h16" />
                                                </svg>
                                            </button>
                                        </div>
                                    </div>
                                {% endfor %}
                            </div>
                        {% else %}
                            <div class="text-center py-8">
                                <svg class="mx-auto h-12 w-12 text-gray-400" xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 8c-1.657 0-3 .895-3 2s1.343 2 3 2 3 .895 3 2-1.343 2-3 2m0-8c1.11 0 2.08.402 2.599 1M12 8V7m0 1v8m0 0v1m0-1c-1.11 0-2.08-.402-2.599-1" />
                                </svg>
                                <h3 class="mt-2 text-sm font-medium text-gray-900">Aucun prêt</h3>
                                <p class="mt-1 text-sm text-gray-500">Commencez par ajouter un prêt pour ce projet.</p>
                                <div class="mt-6">
                                    <button onclick="openLoanModal()" 
                                            class="inline-flex items-center px-4 py-2 border border-transparent shadow-sm text-sm font-medium rounded-md text-white bg-indigo-600 hover:bg-indigo-700">
                                        <svg class="-ml-1 mr-2 h-5 w-5" xmlns="http://www.w3.org/2000/svg" viewBox="0 0 20 20" fill="currentColor">
                                            <path fill-rule="evenodd" d="M10 3a1 1 0 011 1v5h5a1 1 0 110 2h-5v5a1 1 0 11-2 0v-5H4a1 1 0 110-2h5V4a1 1 0 011-1z" clip-rule="evenodd" />
                                        </svg>
                                        Ajouter un prêt
                                    </button>
                                </div>
                            </div>
                        {% endif %}
                    </div>
                </div>
            </div>

            <!-- Sidebar -->
            <div class="space-y-8">
                <!-- Initial Charges -->
                <div class="bg-white shadow rounded-lg">
                    <div class="px-6 py-4 border-b border-gray-200 flex justify-between items-center">
                        <h3 class="text-lg font-medium text-gray-900">Charges initiales</h3>
                        <button data-controller="modal"
                                data-modal-project-id-value="{{ project.id }}"
                                data-action="click->modal#configure"
                                data-modal-type-param="initial"
                                class="inline-flex items-center px-2 py-1 border border-transparent text-xs font-medium rounded text-indigo-700 bg-indigo-100 hover:bg-indigo-200">
                            <svg class="-ml-0.5 mr-1 h-3 w-3" xmlns="http://www.w3.org/2000/svg" viewBox="0 0 20 20" fill="currentColor">
                                <path fill-rule="evenodd" d="M10 3a1 1 0 011 1v5h5a1 1 0 110 2h-5v5a1 1 0 11-2 0v-5H4a1 1 0 110-2h5V4a1 1 0 011-1z" clip-rule="evenodd" />
                            </svg>
                            Ajouter
                        </button>
                    </div>
                    <div class="px-6 py-4">
                        {% if initialCharges|length > 0 %}
                            <div id="initial-charges-container" class="space-y-3">
                                {% for charge in initialCharges %}
                                    <div class="flex justify-between items-center charge-item" data-charge-id="{{ charge.id }}">
                                        <div class="flex-1">
                                            <p class="text-sm font-medium text-gray-900">{{ charge.label }}</p>
                                            <p class="text-sm text-gray-500">{{ charge.amount|number_format(2, ',', ' ') }} €</p>
                                        </div>
                                        <button onclick="deleteCharge({{ charge.id }})" 
                                                class="text-red-600 hover:text-red-800">
                                            <svg class="h-4 w-4" xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M6 18L18 6M6 6l12 12" />
                                            </svg>
                                        </button>
                                    </div>
                                {% endfor %}
                            </div>
                        {% else %}
                            <p class="text-sm text-gray-500 text-center py-4">Aucune charge initiale</p>
                        {% endif %}
                    </div>
                </div>

                <!-- Recurring Charges -->
                <div class="bg-white shadow rounded-lg">
                    <div class="px-6 py-4 border-b border-gray-200 flex justify-between items-center">
                        <h3 class="text-lg font-medium text-gray-900">Charges récurrentes</h3>
                        <button data-controller="modal"
                                data-modal-project-id-value="{{ project.id }}"
                                data-action="click->modal#configure"
                                data-modal-type-param="recurring"
                                class="inline-flex items-center px-2 py-1 border border-transparent text-xs font-medium rounded text-indigo-700 bg-indigo-100 hover:bg-indigo-200">
                            <svg class="-ml-0.5 mr-1 h-3 w-3" xmlns="http://www.w3.org/2000/svg" viewBox="0 0 20 20" fill="currentColor">
                                <path fill-rule="evenodd" d="M10 3a1 1 0 011 1v5h5a1 1 0 110 2h-5v5a1 1 0 11-2 0v-5H4a1 1 0 110-2h5V4a1 1 0 011-1z" clip-rule="evenodd" />
                            </svg>
                            Ajouter
                        </button>
                    </div>
                    <div class="px-6 py-4">
                        {% if recurringCharges|length > 0 %}
                            <div id="recurring-charges-container" class="space-y-3">
                                {% for charge in recurringCharges %}
                                    <div class="flex justify-between items-center charge-item" data-charge-id="{{ charge.id }}">
                                        <div class="flex-1">
                                            <p class="text-sm font-medium text-gray-900">{{ charge.label }}</p>
                                            <p class="text-sm text-gray-500">
                                                {{ charge.amount|number_format(2, ',', ' ') }} € 
                                                {% if charge.frequency %}
                                                    ({{ charge.frequency }})
                                                {% endif %}
                                            </p>
                                        </div>
                                        <button onclick="deleteCharge({{ charge.id }})" 
                                                class="text-red-600 hover:text-red-800">
                                            <svg class="h-4 w-4" xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M6 18L18 6M6 6l12 12" />
                                            </svg>
                                        </button>
                                    </div>
                                {% endfor %}
                            </div>
                        {% else %}
                            <p class="text-sm text-gray-500 text-center py-4">Aucune charge récurrente</p>
                        {% endif %}
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

<!-- Loan Modal -->
<div data-controller="modal"
     data-modal-target="modal"
     data-action="click->modal#handleBackdrop"
     class="fixed inset-0 bg-gray-600 bg-opacity-50 overflow-y-auto h-full w-full hidden z-50">
    <div class="relative top-20 mx-auto p-5 border w-full max-w-md shadow-lg rounded-md bg-white">
        <div class="mt-3">
            <h3 data-modal-target="title" class="text-lg font-medium text-gray-900 text-center">Ajouter un prêt</h3>
            <form data-modal-target="form"
                  data-action="submit->modal#submit"
                  action="{{ path('app_project_loan_add', {id: project.id}) }}"
                  class="mt-4 space-y-4">
                <div>
                    <label for="loanAmount" class="block text-sm font-medium text-gray-700">Montant (€)</label>
                    <input type="number" id="loanAmount" name="amount" step="0.01" required
                           class="mt-1 block w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-indigo-500 focus:border-indigo-500">
                </div>
                <div class="grid grid-cols-2 gap-4">
                    <div>
                        <label for="loanInterestRate" class="block text-sm font-medium text-gray-700">Taux d'intérêt (%)</label>
                        <input type="number" id="loanInterestRate" name="interestRate" step="0.01" required
                               class="mt-1 block w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-indigo-500 focus:border-indigo-500">
                    </div>
                    <div>
                        <label for="loanInsuranceRate" class="block text-sm font-medium text-gray-700">Taux d'assurance (%)</label>
                        <input type="number" id="loanInsuranceRate" name="insuranceRate" step="0.01" required
                               class="mt-1 block w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-indigo-500 focus:border-indigo-500">
                    </div>
                </div>
                <div class="grid grid-cols-2 gap-4">
                    <div>
                        <label for="loanDuration" class="block text-sm font-medium text-gray-700">Durée (années)</label>
                        <input type="number" id="loanDuration" name="durationYears" required
                               class="mt-1 block w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-indigo-500 focus:border-indigo-500">
                    </div>
                    <div>
                        <label for="loanDeferred" class="block text-sm font-medium text-gray-700">Différé (mois)</label>
                        <input type="number" id="loanDeferred" name="deferredMonths" value="0"
                               class="mt-1 block w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-indigo-500 focus:border-indigo-500">
                    </div>
                </div>
                <div>
                    <label for="loanStartDate" class="block text-sm font-medium text-gray-700">Date de début</label>
                    <input type="date" id="loanStartDate" name="startDate" required
                           class="mt-1 block w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-indigo-500 focus:border-indigo-500">
                </div>
                <div class="flex items-center justify-end space-x-3 mt-6">
                    <button type="button"
                            data-action="click->modal#close"
                            class="px-4 py-2 bg-gray-300 text-gray-700 rounded-md hover:bg-gray-400 transition-colors">
                        Annuler
                    </button>
                    <button type="submit"
                            class="px-4 py-2 bg-indigo-600 text-white rounded-md hover:bg-indigo-700 transition-colors">
                        Ajouter
                    </button>
                </div>
            </form>
        </div>
    </div>
</div>

<!-- Charge Modal -->
<div data-controller="modal"
     data-modal-target="modal"
     data-action="click->modal#handleBackdrop"
     class="fixed inset-0 bg-gray-600 bg-opacity-50 overflow-y-auto h-full w-full hidden z-50">
    <div class="relative top-20 mx-auto p-5 border w-full max-w-md shadow-lg rounded-md bg-white">
        <div class="mt-3">
            <h3 data-modal-target="title" class="text-lg font-medium text-gray-900 text-center">Ajouter une charge</h3>
            <form data-modal-target="form"
                  data-action="submit->modal#submit"
                  action="{{ path('app_project_charge_add', {id: project.id}) }}"
                  class="mt-4 space-y-4">
                <input type="hidden" id="chargeType" name="type">
                <div>
                    <label for="chargeLabel" class="block text-sm font-medium text-gray-700">Libellé</label>
                    <input type="text" id="chargeLabel" name="label" required
                           class="mt-1 block w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-indigo-500 focus:border-indigo-500">
                </div>
                <div>
                    <label for="chargeAmount" class="block text-sm font-medium text-gray-700">Montant (€)</label>
                    <input type="number" id="chargeAmount" name="amount" step="0.01" required
                           class="mt-1 block w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-indigo-500 focus:border-indigo-500">
                </div>
                <div id="frequencyField" class="hidden">
                    <label for="chargeFrequency" class="block text-sm font-medium text-gray-700">Fréquence</label>
                    <select id="chargeFrequency" name="frequency"
                            class="mt-1 block w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-indigo-500 focus:border-indigo-500">
                        <option value="">Sélectionner une fréquence</option>
                        <option value="weekly">Hebdomadaire</option>
                        <option value="monthly">Mensuelle</option>
                        <option value="quarterly">Trimestrielle</option>
                        <option value="yearly">Annuelle</option>
                    </select>
                </div>
                <div>
                    <label for="chargeStartDate" class="block text-sm font-medium text-gray-700">Date de début</label>
                    <input type="date" id="chargeStartDate" name="startDate" required
                           class="mt-1 block w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-indigo-500 focus:border-indigo-500">
                </div>
                <div>
                    <label for="chargeDescription" class="block text-sm font-medium text-gray-700">Description (optionnel)</label>
                    <textarea id="chargeDescription" name="description" rows="3"
                              class="mt-1 block w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-indigo-500 focus:border-indigo-500"></textarea>
                </div>
                <div class="flex items-center justify-end space-x-3 mt-6">
                    <button type="button"
                            data-action="click->modal#close"
                            class="px-4 py-2 bg-gray-300 text-gray-700 rounded-md hover:bg-gray-400 transition-colors">
                        Annuler
                    </button>
                    <button type="submit"
                            class="px-4 py-2 bg-indigo-600 text-white rounded-md hover:bg-indigo-700 transition-colors">
                        Ajouter
                    </button>
                </div>
            </form>
        </div>
    </div>
</div>

<script>
const projectId = {{ project.id }};

// Inline editing functionality
document.addEventListener('DOMContentLoaded', function() {
    document.querySelectorAll('.editable-field').forEach(function(element) {
        element.addEventListener('click', function() {
            makeEditable(this);
        });
    });
});

function makeEditable(element) {
    if (element.querySelector('input')) return; // Already editing

    const field = element.dataset.field;
    const currentValue = element.textContent.trim();
    const type = element.dataset.type || 'text';
    const suffix = element.dataset.suffix || '';

    // Remove suffix from current value for editing
    let editValue = currentValue.replace(suffix, '').replace(/\s/g, '').replace(',', '.');
    if (editValue === 'Non défini') editValue = '';

    const input = document.createElement('input');
    input.type = type;
    input.value = editValue;
    input.className = 'w-full px-2 py-1 border border-gray-300 rounded focus:outline-none focus:ring-2 focus:ring-indigo-500';

    element.innerHTML = '';
    element.appendChild(input);
    input.focus();
    input.select();

    function saveEdit() {
        const newValue = input.value;

        fetch(`/projet/${projectId}/edit`, {
            method: 'POST',
            headers: {
                'Content-Type': 'application/x-www-form-urlencoded',
                'X-Requested-With': 'XMLHttpRequest'
            },
            body: `field=${field}&value=${encodeURIComponent(newValue)}`
        })
        .then(response => response.json())
        .then(data => {
            if (data.success) {
                let displayValue = newValue;
                if (displayValue && type === 'number') {
                    displayValue = parseFloat(newValue).toLocaleString('fr-FR', {
                        minimumFractionDigits: 2,
                        maximumFractionDigits: 2
                    });
                }
                element.textContent = displayValue ? displayValue + suffix : 'Non défini';
            } else {
                alert(data.message || 'Erreur lors de la mise à jour');
                element.textContent = currentValue;
            }
        })
        .catch(error => {
            console.error('Error:', error);
            element.textContent = currentValue;
        });
    }

    function cancelEdit() {
        element.textContent = currentValue;
    }

    input.addEventListener('blur', saveEdit);
    input.addEventListener('keydown', function(e) {
        if (e.key === 'Enter') {
            e.preventDefault();
            saveEdit();
        } else if (e.key === 'Escape') {
            e.preventDefault();
            cancelEdit();
        }
    });
}

// Loan modal functions
function openLoanModal() {
    document.getElementById('loanModal').classList.remove('hidden');
    document.getElementById('loanStartDate').value = new Date().toISOString().split('T')[0];
}

function closeLoanModal() {
    document.getElementById('loanModal').classList.add('hidden');
    document.getElementById('loanForm').reset();
}

document.getElementById('loanForm').addEventListener('submit', function(e) {
    e.preventDefault();

    const formData = new FormData(this);

    fetch(`/projet/${projectId}/loan/add`, {
        method: 'POST',
        body: formData
    })
    .then(response => response.json())
    .then(data => {
        if (data.success) {
            location.reload(); // Reload to show the new loan
        } else {
            alert(data.message || 'Erreur lors de l\'ajout du prêt');
        }
    })
    .catch(error => {
        console.error('Error:', error);
        alert('Une erreur est survenue');
    });
});

function deleteLoan(loanId) {
    if (confirm('Êtes-vous sûr de vouloir supprimer ce prêt ?')) {
        fetch(`/projet/${projectId}/loan/${loanId}/delete`, {
            method: 'DELETE',
            headers: {
                'X-Requested-With': 'XMLHttpRequest'
            }
        })
        .then(response => response.json())
        .then(data => {
            if (data.success) {
                location.reload();
            } else {
                alert(data.message || 'Erreur lors de la suppression');
            }
        })
        .catch(error => {
            console.error('Error:', error);
            alert('Une erreur est survenue');
        });
    }
}

// Charge modal functions
function openChargeModal(type) {
    const modal = document.getElementById('chargeModal');
    const title = document.getElementById('chargeModalTitle');
    const typeInput = document.getElementById('chargeType');
    const frequencyField = document.getElementById('frequencyField');

    typeInput.value = type;

    if (type === 'initial') {
        title.textContent = 'Ajouter une charge initiale';
        frequencyField.classList.add('hidden');
    } else {
        title.textContent = 'Ajouter une charge récurrente';
        frequencyField.classList.remove('hidden');
    }

    document.getElementById('chargeStartDate').value = new Date().toISOString().split('T')[0];
    modal.classList.remove('hidden');
}

function closeChargeModal() {
    document.getElementById('chargeModal').classList.add('hidden');
    document.getElementById('chargeForm').reset();
}

document.getElementById('chargeForm').addEventListener('submit', function(e) {
    e.preventDefault();

    const formData = new FormData(this);

    fetch(`/projet/${projectId}/charge/add`, {
        method: 'POST',
        body: formData
    })
    .then(response => response.json())
    .then(data => {
        if (data.success) {
            location.reload(); // Reload to show the new charge
        } else {
            alert(data.message || 'Erreur lors de l\'ajout de la charge');
        }
    })
    .catch(error => {
        console.error('Error:', error);
        alert('Une erreur est survenue');
    });
});

function deleteCharge(chargeId) {
    if (confirm('Êtes-vous sûr de vouloir supprimer cette charge ?')) {
        fetch(`/projet/${projectId}/charge/${chargeId}/delete`, {
            method: 'DELETE',
            headers: {
                'X-Requested-With': 'XMLHttpRequest'
            }
        })
        .then(response => response.json())
        .then(data => {
            if (data.success) {
                location.reload();
            } else {
                alert(data.message || 'Erreur lors de la suppression');
            }
        })
        .catch(error => {
            console.error('Error:', error);
            alert('Une erreur est survenue');
        });
    }
}

// Close modals when clicking outside
document.getElementById('loanModal').addEventListener('click', function(e) {
    if (e.target === this) closeLoanModal();
});

document.getElementById('chargeModal').addEventListener('click', function(e) {
    if (e.target === this) closeChargeModal();
});
</script>
{% endblock %}
