<?php

namespace App\Entity;

use App\Repository\LoanRepository;
use Doctrine\ORM\Mapping as ORM;

#[ORM\Entity(repositoryClass: LoanRepository::class)]
class Loan
{
    #[ORM\Id]
    #[ORM\GeneratedValue]
    #[ORM\Column(type: 'integer')]
    private ?int $id = null;

    #[ORM\ManyToOne(targetEntity: Project::class, inversedBy: 'loans')]
    #[ORM\JoinColumn(nullable: false)]
    private ?Project $project = null;

    #[ORM\Column(type: 'decimal', precision: 10, scale: 2)]
    private string $amount;

    #[ORM\Column(type: 'decimal', precision: 5, scale: 2)]
    private string $interestRate;

    #[ORM\Column(type: 'decimal', precision: 5, scale: 2)]
    private string $insuranceRate;

    #[ORM\Column(type: 'integer')]
    private int $durationYears;

    #[ORM\Column(type: 'date')]
    private DateTimeInterface $startDate;

    #[ORM\Column(type: 'integer', options: ['default' => 0])]
    private int $deferredMonths = 0;

    public function getId(): ?int
    {
        return $this->id;
    }

    public function getProject(): ?Project
    {
        return $this->project;
    }

    public function setProject(Project $project): self
    {
        $this->project = $project;

        return $this;
    }

    public function getAmount(): string
    {
        return $this->amount;
    }

    public function setAmount(string $amount): self
    {
        $this->amount = $amount;

        return $this;
    }

    public function getInterestRate(): string
    {
        return $this->interestRate;
    }

    public function setInterestRate(string $interestRate): self
    {
        $this->interestRate = $interestRate;

        return $this;
    }

    public function getInsuranceRate(): string
    {
        return $this->insuranceRate;
    }

    public function setInsuranceRate(string $insuranceRate): self
    {
        $this->insuranceRate = $insuranceRate;

        return $this;
    }

    public function getDurationYears(): int
    {
        return $this->durationYears;
    }

    public function setDurationYears(int $durationYears): self
    {
        $this->durationYears = $durationYears;

        return $this;
    }

    public function getStartDate(): DateTimeInterface
    {
        return $this->startDate;
    }

    public function setStartDate(DateTimeInterface $startDate): self
    {
        $this->startDate = $startDate;

        return $this;
    }

    public function getDeferredMonths(): int
    {
        return $this->deferredMonths;
    }

    public function setDeferredMonths(int $deferredMonths): self
    {
        $this->deferredMonths = $deferredMonths;

        return $this;
    }
}