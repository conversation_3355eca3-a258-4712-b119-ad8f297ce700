<?php

namespace App\Repository;

use App\Entity\Loan;
use Doctrine\Bundle\DoctrineBundle\Repository\ServiceEntityRepository;
use Doctrine\Persistence\ManagerRegistry;

/**
 * @extends ServiceEntityRepository<Loan>
 */
class LoanRepository extends ServiceEntityRepository
{
    public function __construct(ManagerRegistry $registry)
    {
        parent::__construct($registry, Loan::class);
    }

    /**
     * Find loans by project
     */
    public function findByProject($project): array
    {
        return $this->createQueryBuilder('l')
            ->andWhere('l.project = :project')
            ->setParameter('project', $project)
            ->orderBy('l.startDate', 'ASC')
            ->getQuery()
            ->getResult();
    }

    /**
     * Find active loans (not yet fully paid)
     */
    public function findActiveLoans($project): array
    {
        return $this->createQueryBuilder('l')
            ->andWhere('l.project = :project')
            ->andWhere('l.startDate <= :now')
            ->setParameter('project', $project)
            ->setParameter('now', new \DateTime())
            ->orderBy('l.startDate', 'ASC')
            ->getQuery()
            ->getResult();
    }
}
