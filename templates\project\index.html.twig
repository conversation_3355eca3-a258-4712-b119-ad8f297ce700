{% extends 'base.html.twig' %}

{% block title %}Mes Projets - OptiImmo{% endblock %}

{% block body %}
<div class="min-h-screen bg-gray-50 py-8">
    <div class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
        <!-- Header -->
        <div class="md:flex md:items-center md:justify-between">
            <div class="flex-1 min-w-0">
                <h2 class="text-2xl font-bold leading-7 text-gray-900 sm:text-3xl sm:truncate">
                    Mes Projets Immobiliers
                </h2>
                <p class="mt-1 text-sm text-gray-500">
                    <PERSON><PERSON><PERSON> et suivez tous vos projets d'investissement immobilier
                </p>
            </div>
            <div class="mt-4 flex md:mt-0 md:ml-4">
                <button type="button" 
                        onclick="openNewProjectModal()"
                        class="ml-3 inline-flex items-center px-4 py-2 border border-transparent rounded-md shadow-sm text-sm font-medium text-white bg-indigo-600 hover:bg-indigo-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-indigo-500">
                    <svg class="-ml-1 mr-2 h-5 w-5" xmlns="http://www.w3.org/2000/svg" viewBox="0 0 20 20" fill="currentColor">
                        <path fill-rule="evenodd" d="M10 3a1 1 0 011 1v5h5a1 1 0 110 2h-5v5a1 1 0 11-2 0v-5H4a1 1 0 110-2h5V4a1 1 0 011-1z" clip-rule="evenodd" />
                    </svg>
                    Nouveau Projet
                </button>
            </div>
        </div>

        <!-- Projects Grid -->
        <div class="mt-8">
            {% if projects|length > 0 %}
                <div class="grid grid-cols-1 gap-6 sm:grid-cols-2 lg:grid-cols-3">
                    {% for project in projects %}
                        <div class="bg-white overflow-hidden shadow rounded-lg hover:shadow-lg transition-shadow duration-200">
                            <div class="p-6">
                                <div class="flex items-center">
                                    <div class="flex-shrink-0">
                                        <div class="h-10 w-10 rounded-lg bg-indigo-500 flex items-center justify-center">
                                            <svg class="h-6 w-6 text-white" xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M19 21V5a2 2 0 00-2-2H7a2 2 0 00-2 2v16m14 0h2m-2 0h-6m-2-5.5v3m0 0h3m-3 0h-3m6-3h2M7 13h3m-3 4h3m-3 4h3m6-3v-1M9 7h6" />
                                            </svg>
                                        </div>
                                    </div>
                                    <div class="ml-4 flex-1">
                                        <h3 class="text-lg font-medium text-gray-900 truncate">
                                            {{ project.name }}
                                        </h3>
                                        <p class="text-sm text-gray-500">
                                            Créé le {{ project.createdAt|date('d/m/Y') }}
                                        </p>
                                    </div>
                                </div>
                                
                                <div class="mt-4">
                                    <div class="flex justify-between text-sm text-gray-600">
                                        <span>Prêts: {{ project.loans|length }}</span>
                                        <span>Charges: {{ project.charges|length }}</span>
                                    </div>
                                    {% if project.rentAmount %}
                                        <div class="mt-2 text-sm text-gray-600">
                                            Loyer: {{ project.rentAmount|number_format(2, ',', ' ') }} €
                                        </div>
                                    {% endif %}
                                </div>

                                <div class="mt-6 flex space-x-3">
                                    <a href="{{ path('app_project_show', {id: project.id}) }}" 
                                       class="flex-1 bg-indigo-600 text-white text-center px-4 py-2 rounded-md text-sm font-medium hover:bg-indigo-700 transition-colors">
                                        Voir le projet
                                    </a>
                                    <button onclick="deleteProject({{ project.id }})" 
                                            class="px-3 py-2 border border-red-300 text-red-700 rounded-md text-sm font-medium hover:bg-red-50 transition-colors">
                                        <svg class="h-4 w-4" xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M19 7l-.867 12.142A2 2 0 0116.138 21H7.862a2 2 0 01-1.995-1.858L5 7m5 4v6m4-6v6m1-10V4a1 1 0 00-1-1h-4a1 1 0 00-1 1v3M4 7h16" />
                                        </svg>
                                    </button>
                                </div>
                            </div>
                        </div>
                    {% endfor %}
                </div>
            {% else %}
                <!-- Empty State -->
                <div class="text-center py-12">
                    <svg class="mx-auto h-12 w-12 text-gray-400" xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M19 21V5a2 2 0 00-2-2H7a2 2 0 00-2 2v16m14 0h2m-2 0h-6m-2-5.5v3m0 0h3m-3 0h-3m6-3h2M7 13h3m-3 4h3m-3 4h3m6-3v-1M9 7h6" />
                    </svg>
                    <h3 class="mt-2 text-sm font-medium text-gray-900">Aucun projet</h3>
                    <p class="mt-1 text-sm text-gray-500">Commencez par créer votre premier projet d'investissement immobilier.</p>
                    <div class="mt-6">
                        <button type="button" 
                                onclick="openNewProjectModal()"
                                class="inline-flex items-center px-4 py-2 border border-transparent shadow-sm text-sm font-medium rounded-md text-white bg-indigo-600 hover:bg-indigo-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-indigo-500">
                            <svg class="-ml-1 mr-2 h-5 w-5" xmlns="http://www.w3.org/2000/svg" viewBox="0 0 20 20" fill="currentColor">
                                <path fill-rule="evenodd" d="M10 3a1 1 0 011 1v5h5a1 1 0 110 2h-5v5a1 1 0 11-2 0v-5H4a1 1 0 110-2h5V4a1 1 0 011-1z" clip-rule="evenodd" />
                            </svg>
                            Nouveau Projet
                        </button>
                    </div>
                </div>
            {% endif %}
        </div>
    </div>
</div>

<!-- New Project Modal -->
<div id="newProjectModal" class="fixed inset-0 bg-gray-600 bg-opacity-50 overflow-y-auto h-full w-full hidden z-50">
    <div class="relative top-20 mx-auto p-5 border w-96 shadow-lg rounded-md bg-white">
        <div class="mt-3">
            <h3 class="text-lg font-medium text-gray-900 text-center">Créer un nouveau projet</h3>
            <form id="newProjectForm" class="mt-4">
                <div>
                    <label for="projectName" class="block text-sm font-medium text-gray-700">Nom du projet</label>
                    <input type="text" 
                           id="projectName" 
                           name="name" 
                           required
                           class="mt-1 block w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-indigo-500 focus:border-indigo-500"
                           placeholder="Ex: Appartement Rue de la Paix">
                </div>
                <div class="flex items-center justify-end space-x-3 mt-6">
                    <button type="button" 
                            onclick="closeNewProjectModal()"
                            class="px-4 py-2 bg-gray-300 text-gray-700 rounded-md hover:bg-gray-400 transition-colors">
                        Annuler
                    </button>
                    <button type="submit" 
                            class="px-4 py-2 bg-indigo-600 text-white rounded-md hover:bg-indigo-700 transition-colors">
                        Créer
                    </button>
                </div>
            </form>
        </div>
    </div>
</div>

<script>
function openNewProjectModal() {
    document.getElementById('newProjectModal').classList.remove('hidden');
    document.getElementById('projectName').focus();
}

function closeNewProjectModal() {
    document.getElementById('newProjectModal').classList.add('hidden');
    document.getElementById('newProjectForm').reset();
}

document.getElementById('newProjectForm').addEventListener('submit', function(e) {
    e.preventDefault();
    
    const formData = new FormData(this);
    
    fetch('{{ path('app_project_new') }}', {
        method: 'POST',
        body: formData
    })
    .then(response => {
        if (response.redirected) {
            window.location.href = response.url;
        }
    })
    .catch(error => {
        console.error('Error:', error);
        alert('Une erreur est survenue lors de la création du projet.');
    });
});

function deleteProject(projectId) {
    if (confirm('Êtes-vous sûr de vouloir supprimer ce projet ? Cette action est irréversible.')) {
        fetch(`/projet/${projectId}/delete`, {
            method: 'DELETE',
            headers: {
                'X-Requested-With': 'XMLHttpRequest'
            }
        })
        .then(response => response.json())
        .then(data => {
            if (data.success) {
                location.reload();
            } else {
                alert(data.message || 'Une erreur est survenue lors de la suppression.');
            }
        })
        .catch(error => {
            console.error('Error:', error);
            alert('Une erreur est survenue lors de la suppression.');
        });
    }
}

// Close modal when clicking outside
document.getElementById('newProjectModal').addEventListener('click', function(e) {
    if (e.target === this) {
        closeNewProjectModal();
    }
});
</script>
{% endblock %}
