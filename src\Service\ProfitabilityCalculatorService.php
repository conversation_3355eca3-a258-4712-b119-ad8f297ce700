<?php

namespace App\Service;

use App\Entity\Project;

class ProfitabilityCalculatorService
{
    public function __construct(
        private LoanCalculatorService $loanCalculator,
        private ExpenseCalculatorService $expenseCalculator
    ) {}

    /**
     * Calculate monthly cash flow
     */
    public function calculateMonthlyCashFlow(Project $project): array
    {
        // Monthly income
        $monthlyRent = (float) ($project->getRentAmount() ?? 0);
        $vacancyRate = (float) ($project->getVacancyRate() ?? 0) / 100;
        $effectiveMonthlyRent = $monthlyRent * (1 - $vacancyRate);

        // Monthly expenses
        $monthlyLoanPayments = 0;
        if ($project->getLoans()->count() > 0) {
            $loansStats = $this->loanCalculator->calculateMultipleLoansStatistics($project->getLoans()->toArray());
            $monthlyLoanPayments = $loansStats['summary']['totalMonthlyPayment'];
        }

        $monthlyRecurringExpenses = $this->expenseCalculator->calculateTotalMonthlyExpenses($project);

        // Net cash flow
        $netMonthlyCashFlow = $effectiveMonthlyRent - $monthlyLoanPayments - $monthlyRecurringExpenses;

        return [
            'income' => [
                'grossRent' => $monthlyRent,
                'vacancyRate' => $vacancyRate * 100,
                'vacancyAmount' => $monthlyRent * $vacancyRate,
                'netRent' => $effectiveMonthlyRent
            ],
            'expenses' => [
                'loanPayments' => $monthlyLoanPayments,
                'recurringExpenses' => $monthlyRecurringExpenses,
                'totalExpenses' => $monthlyLoanPayments + $monthlyRecurringExpenses
            ],
            'cashFlow' => [
                'monthly' => $netMonthlyCashFlow,
                'yearly' => $netMonthlyCashFlow * 12
            ]
        ];
    }

    /**
     * Calculate return on investment (ROI)
     */
    public function calculateROI(Project $project): array
    {
        $totalInvestment = $this->calculateTotalInvestment($project);
        $cashFlow = $this->calculateMonthlyCashFlow($project);
        $yearlyNetIncome = $cashFlow['cashFlow']['yearly'];

        $roi = $totalInvestment > 0 ? ($yearlyNetIncome / $totalInvestment) * 100 : 0;

        return [
            'totalInvestment' => $totalInvestment,
            'yearlyNetIncome' => $yearlyNetIncome,
            'roi' => $roi,
            'paybackPeriod' => $yearlyNetIncome > 0 ? $totalInvestment / $yearlyNetIncome : null
        ];
    }

    /**
     * Calculate total investment
     */
    public function calculateTotalInvestment(Project $project): float
    {
        $propertyValue = (float) ($project->getPropertyValue() ?? 0);
        $landPrice = (float) ($project->getLandPrice() ?? 0);
        $initialExpenses = $this->expenseCalculator->calculateTotalInitialExpenses($project);

        // Calculate down payment (total investment minus loans)
        $totalLoans = 0;
        if ($project->getLoans()->count() > 0) {
            $loansStats = $this->loanCalculator->calculateMultipleLoansStatistics($project->getLoans()->toArray());
            $totalLoans = $loansStats['summary']['totalPrincipal'];
        }

        $totalPropertyCost = $propertyValue + $landPrice;
        $downPayment = $totalPropertyCost - $totalLoans;

        return $downPayment + $initialExpenses;
    }

    /**
     * Calculate cash-on-cash return
     */
    public function calculateCashOnCashReturn(Project $project): float
    {
        $totalInvestment = $this->calculateTotalInvestment($project);
        $cashFlow = $this->calculateMonthlyCashFlow($project);
        $yearlyNetIncome = $cashFlow['cashFlow']['yearly'];

        return $totalInvestment > 0 ? ($yearlyNetIncome / $totalInvestment) * 100 : 0;
    }

    /**
     * Calculate gross rental yield
     */
    public function calculateGrossRentalYield(Project $project): float
    {
        $propertyValue = (float) ($project->getPropertyValue() ?? 0);
        $landPrice = (float) ($project->getLandPrice() ?? 0);
        $totalPropertyValue = $propertyValue + $landPrice;
        
        $monthlyRent = (float) ($project->getRentAmount() ?? 0);
        $yearlyRent = $monthlyRent * 12;

        return $totalPropertyValue > 0 ? ($yearlyRent / $totalPropertyValue) * 100 : 0;
    }

    /**
     * Calculate net rental yield
     */
    public function calculateNetRentalYield(Project $project): float
    {
        $propertyValue = (float) ($project->getPropertyValue() ?? 0);
        $landPrice = (float) ($project->getLandPrice() ?? 0);
        $totalPropertyValue = $propertyValue + $landPrice;
        
        $cashFlow = $this->calculateMonthlyCashFlow($project);
        $yearlyNetIncome = $cashFlow['income']['netRent'] * 12 - $cashFlow['expenses']['recurringExpenses'] * 12;

        return $totalPropertyValue > 0 ? ($yearlyNetIncome / $totalPropertyValue) * 100 : 0;
    }

    /**
     * Calculate debt service coverage ratio
     */
    public function calculateDebtServiceCoverageRatio(Project $project): float
    {
        $cashFlow = $this->calculateMonthlyCashFlow($project);
        $monthlyNetIncome = $cashFlow['income']['netRent'] - $cashFlow['expenses']['recurringExpenses'];
        $monthlyLoanPayments = $cashFlow['expenses']['loanPayments'];

        return $monthlyLoanPayments > 0 ? $monthlyNetIncome / $monthlyLoanPayments : 0;
    }

    /**
     * Calculate comprehensive profitability analysis
     */
    public function calculateProfitabilityAnalysis(Project $project): array
    {
        $cashFlow = $this->calculateMonthlyCashFlow($project);
        $roi = $this->calculateROI($project);
        $grossYield = $this->calculateGrossRentalYield($project);
        $netYield = $this->calculateNetRentalYield($project);
        $cashOnCashReturn = $this->calculateCashOnCashReturn($project);
        $dscr = $this->calculateDebtServiceCoverageRatio($project);

        // Calculate break-even analysis
        $breakEvenRent = $cashFlow['expenses']['totalExpenses'];
        $currentRent = $cashFlow['income']['netRent'];
        $rentGapToBreakEven = $breakEvenRent - $currentRent;

        // Calculate profitability score (0-100)
        $profitabilityScore = $this->calculateProfitabilityScore($project);

        return [
            'cashFlow' => $cashFlow,
            'returns' => [
                'roi' => $roi['roi'],
                'grossYield' => $grossYield,
                'netYield' => $netYield,
                'cashOnCashReturn' => $cashOnCashReturn,
                'paybackPeriod' => $roi['paybackPeriod']
            ],
            'ratios' => [
                'debtServiceCoverageRatio' => $dscr,
                'loanToValue' => $this->calculateLoanToValueRatio($project)
            ],
            'breakEven' => [
                'breakEvenRent' => $breakEvenRent,
                'currentRent' => $currentRent,
                'rentGap' => $rentGapToBreakEven,
                'isBreakEven' => $rentGapToBreakEven <= 0
            ],
            'investment' => [
                'totalInvestment' => $roi['totalInvestment'],
                'propertyValue' => (float) ($project->getPropertyValue() ?? 0) + (float) ($project->getLandPrice() ?? 0),
                'initialExpenses' => $this->expenseCalculator->calculateTotalInitialExpenses($project)
            ],
            'score' => [
                'profitabilityScore' => $profitabilityScore,
                'rating' => $this->getProfitabilityRating($profitabilityScore)
            ]
        ];
    }

    /**
     * Calculate loan-to-value ratio
     */
    private function calculateLoanToValueRatio(Project $project): float
    {
        $propertyValue = (float) ($project->getPropertyValue() ?? 0);
        $landPrice = (float) ($project->getLandPrice() ?? 0);
        $totalPropertyValue = $propertyValue + $landPrice;

        $totalLoans = 0;
        if ($project->getLoans()->count() > 0) {
            $loansStats = $this->loanCalculator->calculateMultipleLoansStatistics($project->getLoans()->toArray());
            $totalLoans = $loansStats['summary']['totalPrincipal'];
        }

        return $totalPropertyValue > 0 ? ($totalLoans / $totalPropertyValue) * 100 : 0;
    }

    /**
     * Calculate profitability score (0-100)
     */
    private function calculateProfitabilityScore(Project $project): float
    {
        $score = 0;
        $maxScore = 100;

        // Cash flow score (40 points max)
        $cashFlow = $this->calculateMonthlyCashFlow($project);
        $monthlyCashFlow = $cashFlow['cashFlow']['monthly'];
        if ($monthlyCashFlow > 0) {
            $score += min(40, ($monthlyCashFlow / 500) * 40); // 500€ monthly = max score
        }

        // ROI score (30 points max)
        $roi = $this->calculateROI($project);
        if ($roi['roi'] > 0) {
            $score += min(30, ($roi['roi'] / 15) * 30); // 15% ROI = max score
        }

        // Yield score (20 points max)
        $grossYield = $this->calculateGrossRentalYield($project);
        if ($grossYield > 0) {
            $score += min(20, ($grossYield / 8) * 20); // 8% yield = max score
        }

        // DSCR score (10 points max)
        $dscr = $this->calculateDebtServiceCoverageRatio($project);
        if ($dscr >= 1.2) {
            $score += 10;
        } elseif ($dscr >= 1.0) {
            $score += 5;
        }

        return min($maxScore, $score);
    }

    /**
     * Get profitability rating based on score
     */
    private function getProfitabilityRating(float $score): string
    {
        return match(true) {
            $score >= 80 => 'Excellent',
            $score >= 60 => 'Bon',
            $score >= 40 => 'Moyen',
            $score >= 20 => 'Faible',
            default => 'Très faible'
        };
    }

    /**
     * Calculate projections over time
     */
    public function calculateProjections(Project $project, int $years = 10): array
    {
        $projections = [];
        $analysis = $this->calculateProfitabilityAnalysis($project);
        $yearlyNetIncome = $analysis['cashFlow']['cashFlow']['yearly'];
        $totalInvestment = $analysis['investment']['totalInvestment'];
        
        $cumulativeIncome = 0;
        $cumulativeROI = 0;

        for ($year = 1; $year <= $years; $year++) {
            $cumulativeIncome += $yearlyNetIncome;
            $cumulativeROI = $totalInvestment > 0 ? ($cumulativeIncome / $totalInvestment) * 100 : 0;

            $projections[] = [
                'year' => $year,
                'yearlyIncome' => $yearlyNetIncome,
                'cumulativeIncome' => $cumulativeIncome,
                'cumulativeROI' => $cumulativeROI,
                'breakEvenReached' => $cumulativeIncome >= $totalInvestment
            ];
        }

        return $projections;
    }
}
