<?php

namespace App\Form;

use App\Entity\Project;
use Symfony\Component\Form\AbstractType;
use Symfony\Component\Form\Extension\Core\Type\TextType;
use Symfony\Component\Form\Extension\Core\Type\NumberType;
use Symfony\Component\Form\Extension\Core\Type\IntegerType;
use Symfony\Component\Form\Extension\Core\Type\CheckboxType;
use Symfony\Component\Form\Extension\Core\Type\DateType;
use Symfony\Component\Form\FormBuilderInterface;
use Symfony\Component\OptionsResolver\OptionsResolver;
use Symfony\Component\Validator\Constraints as Assert;

class ProjectType extends AbstractType
{
    public function buildForm(FormBuilderInterface $builder, array $options): void
    {
        $builder
            ->add('name', TextType::class, [
                'label' => 'Nom du projet',
                'attr' => [
                    'class' => 'w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-indigo-500',
                    'placeholder' => 'Nom du projet'
                ],
                'constraints' => [
                    new Assert\NotBlank(['message' => 'Le nom du projet est requis']),
                    new Assert\Length(['max' => 255])
                ]
            ])
            ->add('rentAmount', NumberType::class, [
                'label' => 'Montant du loyer (€)',
                'required' => false,
                'attr' => [
                    'class' => 'w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-indigo-500',
                    'placeholder' => '0.00',
                    'step' => '0.01'
                ],
                'constraints' => [
                    new Assert\PositiveOrZero(['message' => 'Le montant du loyer doit être positif'])
                ]
            ])
            ->add('vacancyRate', NumberType::class, [
                'label' => 'Taux de vacance (%)',
                'required' => false,
                'attr' => [
                    'class' => 'w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-indigo-500',
                    'placeholder' => '0.00',
                    'step' => '0.01',
                    'min' => '0',
                    'max' => '100'
                ],
                'constraints' => [
                    new Assert\Range(['min' => 0, 'max' => 100, 'notInRangeMessage' => 'Le taux de vacance doit être entre 0 et 100%'])
                ]
            ])
            ->add('landPrice', NumberType::class, [
                'label' => 'Prix du terrain (€)',
                'required' => false,
                'attr' => [
                    'class' => 'w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-indigo-500',
                    'placeholder' => '0.00',
                    'step' => '0.01'
                ],
                'constraints' => [
                    new Assert\PositiveOrZero(['message' => 'Le prix du terrain doit être positif'])
                ]
            ])
            ->add('propertyValue', NumberType::class, [
                'label' => 'Valeur du bien (€)',
                'required' => false,
                'attr' => [
                    'class' => 'w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-indigo-500',
                    'placeholder' => '0.00',
                    'step' => '0.01'
                ],
                'constraints' => [
                    new Assert\PositiveOrZero(['message' => 'La valeur du bien doit être positive'])
                ]
            ])
            ->add('amortizationDuration', IntegerType::class, [
                'label' => 'Durée d\'amortissement (années)',
                'required' => false,
                'attr' => [
                    'class' => 'w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-indigo-500',
                    'placeholder' => '20',
                    'min' => '1'
                ],
                'constraints' => [
                    new Assert\Positive(['message' => 'La durée d\'amortissement doit être positive'])
                ]
            ])
            ->add('isSci', CheckboxType::class, [
                'label' => 'Projet dans une SCI',
                'required' => false,
                'attr' => [
                    'class' => 'rounded border-gray-300 text-indigo-600 shadow-sm focus:border-indigo-300 focus:ring focus:ring-indigo-200 focus:ring-opacity-50'
                ],
                'help' => 'Cochez cette case si le projet est détenu par une SCI (pour les futurs calculs d\'imposition)'
            ])
            ->add('rentStartDate', DateType::class, [
                'label' => 'Date de début des loyers',
                'required' => false,
                'widget' => 'single_text',
                'attr' => [
                    'class' => 'mt-1 block w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-indigo-500 focus:border-indigo-500'
                ],
                'help' => 'Date à partir de laquelle les loyers commencent (après travaux par exemple)'
            ]);
    }

    public function configureOptions(OptionsResolver $resolver): void
    {
        $resolver->setDefaults([
            'data_class' => Project::class,
        ]);
    }
}
