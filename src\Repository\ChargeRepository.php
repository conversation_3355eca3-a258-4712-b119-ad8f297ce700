<?php

namespace App\Repository;

use App\Entity\Charge;
use Doctrine\Bundle\DoctrineBundle\Repository\ServiceEntityRepository;
use Doctrine\Persistence\ManagerRegistry;

/**
 * @extends ServiceEntityRepository<Charge>
 */
class ChargeRepository extends ServiceEntityRepository
{
    public function __construct(ManagerRegistry $registry)
    {
        parent::__construct($registry, Charge::class);
    }

    /**
     * Find charges by project
     */
    public function findByProject($project): array
    {
        return $this->createQueryBuilder('c')
            ->andWhere('c.project = :project')
            ->setParameter('project', $project)
            ->orderBy('c.startDate', 'ASC')
            ->getQuery()
            ->getResult();
    }

    /**
     * Find charges by type
     */
    public function findByProjectAndType($project, string $type): array
    {
        return $this->createQueryBuilder('c')
            ->andWhere('c.project = :project')
            ->andWhere('c.type = :type')
            ->setParameter('project', $project)
            ->setParameter('type', $type)
            ->orderBy('c.startDate', 'ASC')
            ->getQuery()
            ->getResult();
    }

    /**
     * Find recurring charges
     */
    public function findRecurringCharges($project): array
    {
        return $this->findByProjectAndType($project, Charge::TYPE_RECURRING);
    }

    /**
     * Find initial charges
     */
    public function findInitialCharges($project): array
    {
        return $this->findByProjectAndType($project, Charge::TYPE_INITIAL);
    }
}
