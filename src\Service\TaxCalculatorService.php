<?php

namespace App\Service;

use App\Entity\Project;

class TaxCalculatorService
{
    public function __construct(
        private LoanCalculatorService $loanCalculator,
        private ExpenseCalculatorService $expenseCalculator
    ) {}

    /**
     * Calculate annual tax table for a project
     */
    public function calculateAnnualTaxTable(Project $project, int $years = 10): array
    {
        $table = [];
        $cumulativeDeficit = 0; // Report de déficits fiscaux
        $cumulativeTreasury = 0;

        // Get basic project data
        $annualRent = ((float) ($project->getRentAmount() ?? 0)) * 12;
        $vacancyRate = (float) ($project->getVacancyRate() ?? 0) / 100;
        $effectiveAnnualRent = $annualRent * (1 - $vacancyRate);

        // Annual recurring charges
        $expenseStats = $this->expenseCalculator->getExpenseStatistics($project);
        $annualRecurringCharges = $expenseStats['breakdown']['recurring']['totalYearly'];

        // Property depreciation
        $propertyValue = (float) ($project->getPropertyValue() ?? 0);
        $depreciationDuration = (int) ($project->getAmortizationDuration() ?? 25); // Default 25 years
        $annualDepreciation = $propertyValue > 0 ? $propertyValue / $depreciationDuration : 0;

        // Get loans data and generate combined schedule
        $loans = $project->getLoans()->toArray();
        $combinedSchedule = [];
        if (!empty($loans)) {
            $combinedSchedule = $this->loanCalculator->generateCombinedAmortizationSchedule($loans);
        }

        // Get rent start date
        $rentStartDate = $project->getRentStartDate();
        $currentDate = new \DateTime();
        $currentDate->setDate($currentDate->format('Y'), 1, 1); // Start of current year

        for ($year = 1; $year <= $years; $year++) {
            $yearDate = clone $currentDate;
            $yearDate->modify('+' . ($year - 1) . ' years');

            // Check if rent has started for this year
            $rentActive = true;
            if ($rentStartDate) {
                $rentActive = $yearDate >= $rentStartDate;
            }

            $currentYearRent = $rentActive ? $effectiveAnnualRent : 0;

            // Calculate loan payments for this year from combined schedule
            $yearlyLoanPayment = 0;
            $yearlyInterest = 0;
            $yearlyInsurance = 0;
            $yearlyCapital = 0;

            if (!empty($combinedSchedule)) {
                // Find payments for this year in the combined schedule
                foreach ($combinedSchedule as $monthData) {
                    $monthDate = $monthData['date'];
                    if ($monthDate->format('Y') == $yearDate->format('Y')) {
                        $yearlyLoanPayment += $monthData['totals']['payment'];
                        $yearlyInterest += $monthData['totals']['interest'];
                        $yearlyInsurance += $monthData['totals']['insurance'];
                        $yearlyCapital += $monthData['totals']['principal'];
                    }
                }
            }

            // Calculate fiscal result
            $fiscalResult = $currentYearRent - $annualRecurringCharges - $yearlyInterest;

            // Apply depreciation only during depreciation period
            if ($year <= $depreciationDuration) {
                $fiscalResult -= $annualDepreciation;
            }

            // Handle deficit carryforward
            $taxableResult = $fiscalResult;
            if ($cumulativeDeficit > 0 && $fiscalResult > 0) {
                $deficitUsed = min($cumulativeDeficit, $fiscalResult);
                $taxableResult = $fiscalResult - $deficitUsed;
                $cumulativeDeficit -= $deficitUsed;
            } elseif ($fiscalResult < 0) {
                $cumulativeDeficit += abs($fiscalResult);
                $taxableResult = 0;
            }

            // Calculate taxes
            $taxes = $this->calculateTaxes($taxableResult, $project->isSci());

            // Calculate annual treasury
            $annualTreasury = $currentYearRent - $annualRecurringCharges - $yearlyLoanPayment - $taxes;
            $cumulativeTreasury += $annualTreasury;

            $table[] = [
                'year' => $year,
                'annualRent' => $currentYearRent,
                'charges' => $annualRecurringCharges,
                'capital' => $yearlyCapital,
                'interest' => $yearlyInterest,
                'insurance' => $yearlyInsurance,
                'totalAnnuity' => $yearlyLoanPayment,
                'depreciation' => $year <= $depreciationDuration ? $annualDepreciation : 0,
                'fiscalResult' => $fiscalResult,
                'taxableResult' => $taxableResult,
                'taxes' => $taxes,
                'annualTreasury' => $annualTreasury,
                'cumulativeTreasury' => $cumulativeTreasury,
                'cumulativeDeficit' => $cumulativeDeficit,
                'rentActive' => $rentActive
            ];
        }

        return $table;
    }

    /**
     * Calculate taxes based on taxable result and entity type
     */
    private function calculateTaxes(float $taxableResult, bool $isSci): float
    {
        if ($taxableResult <= 0) {
            return 0;
        }
        
        if ($isSci) {
            // Corporate tax (Impôt sur les sociétés)
            if ($taxableResult <= 42500) {
                return $taxableResult * 0.15; // 15% up to 42,500€
            } else {
                return (42500 * 0.15) + (($taxableResult - 42500) * 0.25); // 25% above 42,500€
            }
        } else {
            // Personal income tax (simplified calculation)
            // This is a simplified version - real calculation would depend on total income
            return $taxableResult * 0.30; // Approximation for marginal rate
        }
    }

    /**
     * Get tax summary for a project
     */
    public function getTaxSummary(Project $project, int $years = 10): array
    {
        $table = $this->calculateAnnualTaxTable($project, $years);
        
        $totalRent = 0;
        $totalCharges = 0;
        $totalInterest = 0;
        $totalTaxes = 0;
        $totalTreasury = 0;
        $totalDepreciation = 0;
        
        foreach ($table as $row) {
            $totalRent += $row['annualRent'];
            $totalCharges += $row['charges'];
            $totalInterest += $row['interest'];
            $totalTaxes += $row['taxes'];
            $totalTreasury += $row['annualTreasury'];
            $totalDepreciation += $row['depreciation'];
        }
        
        $finalCumulativeTreasury = end($table)['cumulativeTreasury'] ?? 0;
        $finalCumulativeDeficit = end($table)['cumulativeDeficit'] ?? 0;
        
        return [
            'table' => $table,
            'summary' => [
                'totalRent' => $totalRent,
                'totalCharges' => $totalCharges,
                'totalInterest' => $totalInterest,
                'totalTaxes' => $totalTaxes,
                'totalTreasury' => $totalTreasury,
                'totalDepreciation' => $totalDepreciation,
                'finalCumulativeTreasury' => $finalCumulativeTreasury,
                'finalCumulativeDeficit' => $finalCumulativeDeficit,
                'averageAnnualTreasury' => $years > 0 ? $totalTreasury / $years : 0,
                'effectiveTaxRate' => $totalRent > 0 ? ($totalTaxes / $totalRent) * 100 : 0
            ]
        ];
    }

    /**
     * Calculate break-even analysis
     */
    public function calculateBreakEvenAnalysis(Project $project): array
    {
        $taxSummary = $this->getTaxSummary($project, 20);
        $table = $taxSummary['table'];
        
        $breakEvenYear = null;
        $totalInvestment = $this->calculateTotalInvestment($project);
        
        foreach ($table as $row) {
            if ($row['cumulativeTreasury'] >= $totalInvestment) {
                $breakEvenYear = $row['year'];
                break;
            }
        }
        
        return [
            'totalInvestment' => $totalInvestment,
            'breakEvenYear' => $breakEvenYear,
            'breakEvenReached' => $breakEvenYear !== null,
            'finalCumulativeTreasury' => end($table)['cumulativeTreasury'] ?? 0
        ];
    }

    /**
     * Calculate total investment (same as in ProfitabilityCalculatorService)
     */
    private function calculateTotalInvestment(Project $project): float
    {
        $propertyValue = (float) ($project->getPropertyValue() ?? 0);
        $landPrice = (float) ($project->getLandPrice() ?? 0);
        $expenseStats = $this->expenseCalculator->getExpenseStatistics($project);
        $initialExpenses = $expenseStats['breakdown']['initial']['total'];

        // Calculate down payment (total investment minus loans)
        $totalLoans = 0;
        if ($project->getLoans()->count() > 0) {
            $loansStats = $this->loanCalculator->calculateMultipleLoansStatistics($project->getLoans()->toArray());
            $totalLoans = $loansStats['summary']['totalPrincipal'];
        }

        $totalPropertyCost = $propertyValue + $landPrice;
        $downPayment = $totalPropertyCost - $totalLoans;

        return $downPayment + $initialExpenses;
    }

    /**
     * Get tax optimization suggestions
     */
    public function getTaxOptimizationSuggestions(Project $project): array
    {
        $suggestions = [];
        $taxSummary = $this->getTaxSummary($project);
        $effectiveTaxRate = $taxSummary['summary']['effectiveTaxRate'];
        
        if (!$project->isSci() && $effectiveTaxRate > 20) {
            $suggestions[] = [
                'type' => 'entity',
                'title' => 'Considérer une SCI',
                'description' => 'Avec un taux d\'imposition effectif de ' . number_format($effectiveTaxRate, 1) . '%, une SCI pourrait être avantageuse.',
                'priority' => 'high'
            ];
        }
        
        if ($taxSummary['summary']['finalCumulativeDeficit'] > 0) {
            $suggestions[] = [
                'type' => 'deficit',
                'title' => 'Report de déficits disponible',
                'description' => 'Vous avez ' . number_format($taxSummary['summary']['finalCumulativeDeficit'], 0) . '€ de déficits reportables.',
                'priority' => 'medium'
            ];
        }
        
        $depreciationDuration = (int) ($project->getAmortizationDuration() ?? 25);
        if ($depreciationDuration > 20) {
            $suggestions[] = [
                'type' => 'depreciation',
                'title' => 'Optimiser la durée d\'amortissement',
                'description' => 'Réduire la durée d\'amortissement pourrait augmenter les déductions fiscales.',
                'priority' => 'low'
            ];
        }
        
        return $suggestions;
    }
}
