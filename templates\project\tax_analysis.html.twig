{% extends 'base.html.twig' %}

{% block title %}Analyse fiscale - {{ project.name }} - OptiImmo{% endblock %}

{% block body %}
<div class="min-h-screen bg-gray-50 py-8">
    <div class="max-w-full mx-auto px-4 sm:px-6 lg:px-8">
        <!-- Header -->
        <div class="md:flex md:items-center md:justify-between mb-8">
            <div class="flex-1 min-w-0">
                <nav class="flex" aria-label="Breadcrumb">
                    <ol class="flex items-center space-x-4">
                        <li>
                            <a href="{{ path('app_project_index') }}" class="text-gray-400 hover:text-gray-500">
                                <svg class="flex-shrink-0 h-5 w-5" xmlns="http://www.w3.org/2000/svg" viewBox="0 0 20 20" fill="currentColor">
                                    <path d="M10.707 2.293a1 1 0 00-1.414 0l-7 7a1 1 0 001.414 1.414L4 10.414V17a1 1 0 001 1h2a1 1 0 001-1v-2a1 1 0 011-1h2a1 1 0 011 1v2a1 1 0 001 1h2a1 1 0 001-1v-6.586l.293.293a1 1 0 001.414-1.414l-7-7z" />
                                </svg>
                            </a>
                        </li>
                        <li>
                            <div class="flex items-center">
                                <svg class="flex-shrink-0 h-5 w-5 text-gray-300" xmlns="http://www.w3.org/2000/svg" fill="currentColor" viewBox="0 0 20 20">
                                    <path d="M5.555 17.776l8-16 .894.448-8 16-.894-.448z" />
                                </svg>
                                <a href="{{ path('app_project_show', {id: project.id}) }}" class="ml-4 text-sm font-medium text-gray-500 hover:text-gray-700">{{ project.name }}</a>
                            </div>
                        </li>
                        <li>
                            <div class="flex items-center">
                                <svg class="flex-shrink-0 h-5 w-5 text-gray-300" xmlns="http://www.w3.org/2000/svg" fill="currentColor" viewBox="0 0 20 20">
                                    <path d="M5.555 17.776l8-16 .894.448-8 16-.894-.448z" />
                                </svg>
                                <span class="ml-4 text-sm font-medium text-gray-500">Analyse fiscale</span>
                            </div>
                        </li>
                    </ol>
                </nav>
                <h1 class="mt-2 text-2xl font-bold leading-7 text-gray-900 sm:text-3xl sm:truncate">
                    Analyse fiscale et trésorerie
                </h1>
                <p class="mt-1 text-sm text-gray-500">
                    {{ project.name }} - 
                    {% if project.isSci %}
                        <span class="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-blue-100 text-blue-800">SCI</span>
                    {% else %}
                        <span class="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-gray-100 text-gray-800">Personne physique</span>
                    {% endif %}
                </p>
            </div>
        </div>

        <!-- Controls -->
        <div class="bg-white shadow rounded-lg mb-6">
            <div class="px-6 py-4">
                <div class="flex flex-col sm:flex-row sm:items-center sm:justify-between space-y-4 sm:space-y-0">
                    <div class="flex items-center space-x-4">
                        <label for="analysisYears" class="text-sm font-medium text-gray-700">Période d'analyse :</label>
                        <select id="analysisYears" class="px-3 py-2 border border-gray-300 rounded-md text-sm focus:outline-none focus:ring-2 focus:ring-indigo-500">
                            <option value="5">5 ans</option>
                            <option value="10" selected>10 ans</option>
                            <option value="15">15 ans</option>
                            <option value="20">20 ans</option>
                            <option value="25">25 ans</option>
                        </select>
                    </div>
                    
                    <div class="flex items-center space-x-2">
                        <button onclick="exportTaxTable()" class="px-4 py-2 bg-green-600 text-white rounded-md hover:bg-green-700 text-sm">
                            <svg class="-ml-1 mr-2 h-4 w-4 inline" xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 10v6m0 0l-3-3m3 3l3-3m2 8H7a2 2 0 01-2-2V5a2 2 0 012-2h5.586a1 1 0 01.707.293l5.414 5.414a1 1 0 01.293.707V19a2 2 0 01-2 2z" />
                            </svg>
                            Exporter Excel
                        </button>
                        <button onclick="printTaxTable()" class="px-4 py-2 bg-blue-600 text-white rounded-md hover:bg-blue-700 text-sm">
                            <svg class="-ml-1 mr-2 h-4 w-4 inline" xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M17 17h2a2 2 0 002-2v-4a2 2 0 00-2-2H5a2 2 0 00-2 2v4a2 2 0 002 2h2m2 4h6a2 2 0 002-2v-4a2 2 0 00-2-2H9a2 2 0 00-2 2v4a2 2 0 002 2zm8-12V5a2 2 0 00-2-2H9a2 2 0 00-2 2v4h10z" />
                            </svg>
                            Imprimer
                        </button>
                    </div>
                </div>
            </div>
        </div>

        <!-- Summary Cards -->
        <div id="summaryCards" class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6 mb-6">
            <!-- Cards will be populated by JavaScript -->
        </div>

        <!-- Optimization Suggestions -->
        <div id="suggestions" class="mb-6">
            <!-- Suggestions will be populated by JavaScript -->
        </div>

        <!-- Tax Table -->
        <div class="bg-white shadow rounded-lg">
            <div class="px-6 py-4 border-b border-gray-200">
                <h3 class="text-lg font-medium text-gray-900">Tableau fiscal et de trésorerie</h3>
                <p class="mt-1 text-sm text-gray-500">Analyse détaillée année par année</p>
            </div>
            
            <div id="taxTableContainer">
                <div class="text-center py-12">
                    <div class="animate-spin rounded-full h-12 w-12 border-b-2 border-indigo-600 mx-auto"></div>
                    <p class="mt-4 text-sm text-gray-500">Chargement de l'analyse fiscale...</p>
                </div>
            </div>
        </div>
    </div>
</div>

<script>
const projectId = {{ project.id }};
let currentTaxData = null;

document.addEventListener('DOMContentLoaded', function() {
    loadTaxAnalysis();
    
    document.getElementById('analysisYears').addEventListener('change', function() {
        loadTaxAnalysis();
    });
});

function loadTaxAnalysis() {
    const years = document.getElementById('analysisYears').value;
    const container = document.getElementById('taxTableContainer');
    
    container.innerHTML = `
        <div class="text-center py-12">
            <div class="animate-spin rounded-full h-12 w-12 border-b-2 border-indigo-600 mx-auto"></div>
            <p class="mt-4 text-sm text-gray-500">Chargement de l'analyse fiscale...</p>
        </div>
    `;
    
    fetch(`/projet/${projectId}/tax-data?years=${years}`)
        .then(response => response.json())
        .then(data => {
            if (data.success) {
                currentTaxData = data;
                displayTaxAnalysis(data);
            } else {
                showError(data.message);
            }
        })
        .catch(error => {
            console.error('Error:', error);
            showError('Erreur lors du chargement de l\'analyse fiscale');
        });
}

function displayTaxAnalysis(data) {
    displaySummaryCards(data.taxSummary.summary, data.breakEvenAnalysis);
    displaySuggestions(data.suggestions);
    displayTaxTable(data.taxSummary.table);
}

function displaySummaryCards(summary, breakEven) {
    const container = document.getElementById('summaryCards');
    
    container.innerHTML = `
        <div class="bg-white overflow-hidden shadow rounded-lg">
            <div class="p-5">
                <div class="flex items-center">
                    <div class="flex-shrink-0">
                        <svg class="h-6 w-6 text-green-400" xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 8c-1.657 0-3 .895-3 2s1.343 2 3 2 3 .895 3 2-1.343 2-3 2m0-8c1.11 0 2.08.402 2.599 1M12 8V7m0 1v8m0 0v1m0-1c-1.11 0-2.08-.402-2.599-1" />
                        </svg>
                    </div>
                    <div class="ml-5 w-0 flex-1">
                        <dl>
                            <dt class="text-sm font-medium text-gray-500 truncate">Trésorerie finale</dt>
                            <dd class="text-lg font-medium text-gray-900">${summary.finalCumulativeTreasury.toLocaleString('fr-FR', {minimumFractionDigits: 0, maximumFractionDigits: 0})} €</dd>
                        </dl>
                    </div>
                </div>
            </div>
        </div>
        
        <div class="bg-white overflow-hidden shadow rounded-lg">
            <div class="p-5">
                <div class="flex items-center">
                    <div class="flex-shrink-0">
                        <svg class="h-6 w-6 text-red-400" xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 7h6m0 10v-3m-3 3h.01M9 17h.01M9 14h.01M12 14h.01M15 11h.01M12 11h.01M9 11h.01M7 21h10a2 2 0 002-2V5a2 2 0 00-2-2H7a2 2 0 00-2 2v14a2 2 0 002 2z" />
                        </svg>
                    </div>
                    <div class="ml-5 w-0 flex-1">
                        <dl>
                            <dt class="text-sm font-medium text-gray-500 truncate">Impôts totaux</dt>
                            <dd class="text-lg font-medium text-gray-900">${summary.totalTaxes.toLocaleString('fr-FR', {minimumFractionDigits: 0, maximumFractionDigits: 0})} €</dd>
                        </dl>
                    </div>
                </div>
            </div>
        </div>
        
        <div class="bg-white overflow-hidden shadow rounded-lg">
            <div class="p-5">
                <div class="flex items-center">
                    <div class="flex-shrink-0">
                        <svg class="h-6 w-6 text-blue-400" xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 19v-6a2 2 0 00-2-2H5a2 2 0 00-2 2v6a2 2 0 002 2h2a2 2 0 002-2zm0 0V9a2 2 0 012-2h2a2 2 0 012 2v10m-6 0a2 2 0 002 2h2a2 2 0 002-2m0 0V5a2 2 0 012-2h2a2 2 0 012 2v14a2 2 0 01-2 2h-2a2 2 0 01-2-2z" />
                        </svg>
                    </div>
                    <div class="ml-5 w-0 flex-1">
                        <dl>
                            <dt class="text-sm font-medium text-gray-500 truncate">Taux d'imposition effectif</dt>
                            <dd class="text-lg font-medium text-gray-900">${summary.effectiveTaxRate.toFixed(1)} %</dd>
                        </dl>
                    </div>
                </div>
            </div>
        </div>
        
        <div class="bg-white overflow-hidden shadow rounded-lg">
            <div class="p-5">
                <div class="flex items-center">
                    <div class="flex-shrink-0">
                        <svg class="h-6 w-6 text-purple-400" xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M13 7h8m0 0v8m0-8l-8 8-4-4-6 6" />
                        </svg>
                    </div>
                    <div class="ml-5 w-0 flex-1">
                        <dl>
                            <dt class="text-sm font-medium text-gray-500 truncate">Seuil de rentabilité</dt>
                            <dd class="text-lg font-medium text-gray-900">
                                ${breakEven.breakEvenReached ? `Année ${breakEven.breakEvenYear}` : 'Non atteint'}
                            </dd>
                        </dl>
                    </div>
                </div>
            </div>
        </div>
    `;
}

function displaySuggestions(suggestions) {
    const container = document.getElementById('suggestions');
    
    if (suggestions.length === 0) {
        container.innerHTML = '';
        return;
    }
    
    let html = `
        <div class="bg-white shadow rounded-lg">
            <div class="px-6 py-4 border-b border-gray-200">
                <h3 class="text-lg font-medium text-gray-900">Suggestions d'optimisation fiscale</h3>
            </div>
            <div class="px-6 py-4">
                <div class="space-y-4">
    `;
    
    suggestions.forEach(suggestion => {
        const priorityColor = suggestion.priority === 'high' ? 'red' : suggestion.priority === 'medium' ? 'yellow' : 'blue';
        html += `
            <div class="flex items-start space-x-3">
                <div class="flex-shrink-0">
                    <div class="w-2 h-2 bg-${priorityColor}-400 rounded-full mt-2"></div>
                </div>
                <div class="flex-1">
                    <h4 class="text-sm font-medium text-gray-900">${suggestion.title}</h4>
                    <p class="text-sm text-gray-500">${suggestion.description}</p>
                </div>
            </div>
        `;
    });
    
    html += `
                </div>
            </div>
        </div>
    `;
    
    container.innerHTML = html;
}

function displayTaxTable(table) {
    const container = document.getElementById('taxTableContainer');

    let html = `
        <div class="overflow-x-auto">
            <table class="min-w-full divide-y divide-gray-200 text-xs">
                <thead class="bg-gray-50">
                    <tr>
                        <th class="px-2 py-2 text-left font-medium text-gray-500 uppercase tracking-wider text-xs">Année</th>
                        <th class="px-2 py-2 text-center font-medium text-gray-500 uppercase tracking-wider text-xs">Loyers<br>annuel</th>
                        <th class="px-2 py-2 text-center font-medium text-gray-500 uppercase tracking-wider text-xs">Charges</th>
                        <th class="px-2 py-2 text-center font-medium text-gray-500 uppercase tracking-wider text-xs">Capital</th>
                        <th class="px-2 py-2 text-center font-medium text-gray-500 uppercase tracking-wider text-xs">Intérêts</th>
                        <th class="px-2 py-2 text-center font-medium text-gray-500 uppercase tracking-wider text-xs">Assurance</th>
                        <th class="px-2 py-2 text-center font-medium text-gray-500 uppercase tracking-wider text-xs">Annuité<br>total</th>
                        <th class="px-2 py-2 text-center font-medium text-gray-500 uppercase tracking-wider text-xs">Amortisse-<br>ments</th>
                        <th class="px-2 py-2 text-center font-medium text-gray-500 uppercase tracking-wider text-xs">Résultat<br>fiscal</th>
                        <th class="px-2 py-2 text-center font-medium text-gray-500 uppercase tracking-wider text-xs">Impôts<br>en €</th>
                        <th class="px-2 py-2 text-center font-medium text-gray-500 uppercase tracking-wider text-xs">Trésorerie<br>annuel</th>
                        <th class="px-2 py-2 text-center font-medium text-gray-500 uppercase tracking-wider text-xs bg-blue-50">Trésorerie<br>cumulée</th>
                    </tr>
                </thead>
                <tbody class="bg-white divide-y divide-gray-200">
    `;
    
    table.forEach((row, index) => {
        let rowClass = index % 2 === 0 ? 'bg-white' : 'bg-gray-50';
        const treasuryClass = row.cumulativeTreasury >= 0 ? 'text-green-600' : 'text-red-600';
        const fiscalResultClass = row.fiscalResult >= 0 ? 'text-green-600' : 'text-red-600';

        // Highlight rows where rent is not active
        if (row.rentActive === false) {
            rowClass += ' bg-yellow-50 border-l-4 border-yellow-400';
        }
        
        const rentDisplay = row.rentActive === false ?
            `<span class="text-yellow-600">${row.annualRent.toLocaleString('fr-FR', {minimumFractionDigits: 0, maximumFractionDigits: 0})} € <small>(Pas encore de loyers)</small></span>` :
            `${row.annualRent.toLocaleString('fr-FR', {minimumFractionDigits: 0, maximumFractionDigits: 0})} €`;

        html += `
            <tr class="${rowClass} hover:bg-gray-100">
                <td class="px-2 py-2 whitespace-nowrap font-medium text-center">${row.year}</td>
                <td class="px-2 py-2 whitespace-nowrap text-center">${rentDisplay}</td>
                <td class="px-2 py-2 whitespace-nowrap text-center">${row.charges.toLocaleString('fr-FR', {minimumFractionDigits: 0, maximumFractionDigits: 0})} €</td>
                <td class="px-2 py-2 whitespace-nowrap text-center">${row.capital.toLocaleString('fr-FR', {minimumFractionDigits: 0, maximumFractionDigits: 0})} €</td>
                <td class="px-2 py-2 whitespace-nowrap text-center">${row.interest.toLocaleString('fr-FR', {minimumFractionDigits: 0, maximumFractionDigits: 0})} €</td>
                <td class="px-2 py-2 whitespace-nowrap text-center">${row.insurance.toLocaleString('fr-FR', {minimumFractionDigits: 0, maximumFractionDigits: 0})} €</td>
                <td class="px-2 py-2 whitespace-nowrap text-center font-medium">${row.totalAnnuity.toLocaleString('fr-FR', {minimumFractionDigits: 0, maximumFractionDigits: 0})} €</td>
                <td class="px-2 py-2 whitespace-nowrap text-center">${row.depreciation.toLocaleString('fr-FR', {minimumFractionDigits: 0, maximumFractionDigits: 0})} €</td>
                <td class="px-2 py-2 whitespace-nowrap text-center font-medium ${fiscalResultClass}">${row.fiscalResult.toLocaleString('fr-FR', {minimumFractionDigits: 0, maximumFractionDigits: 0})} €</td>
                <td class="px-2 py-2 whitespace-nowrap text-center">${row.taxes.toLocaleString('fr-FR', {minimumFractionDigits: 0, maximumFractionDigits: 0})} €</td>
                <td class="px-2 py-2 whitespace-nowrap text-center font-medium">${row.annualTreasury.toLocaleString('fr-FR', {minimumFractionDigits: 0, maximumFractionDigits: 0})} €</td>
                <td class="px-2 py-2 whitespace-nowrap text-center font-semibold text-base ${treasuryClass} bg-blue-50">${row.cumulativeTreasury.toLocaleString('fr-FR', {minimumFractionDigits: 0, maximumFractionDigits: 0})} €</td>
            </tr>
        `;
    });
    
    html += '</tbody></table></div>';
    
    container.innerHTML = html;
}

function showError(message) {
    document.getElementById('taxTableContainer').innerHTML = `
        <div class="text-center py-8 text-red-600">
            <svg class="mx-auto h-12 w-12 mb-4" xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 8v4m0 4h.01M21 12a9 9 0 11-18 0 9 9 0 0118 0z" />
            </svg>
            <p>${message}</p>
        </div>
    `;
}

function exportTaxTable() {
    alert('Fonctionnalité d\'export Excel en cours de développement');
}

function printTaxTable() {
    window.print();
}
</script>

<style>
/* Full width layout for tax analysis */
.max-w-full {
    max-width: 100%;
}

/* Compact table styling */
table {
    table-layout: fixed;
    width: 100%;
}

table th, table td {
    word-wrap: break-word;
    overflow-wrap: break-word;
}

/* Responsive table columns */
@media (min-width: 1024px) {
    table th:nth-child(1), table td:nth-child(1) { width: 6%; }  /* Année */
    table th:nth-child(2), table td:nth-child(2) { width: 9%; }  /* Loyers */
    table th:nth-child(3), table td:nth-child(3) { width: 8%; }  /* Charges */
    table th:nth-child(4), table td:nth-child(4) { width: 8%; }  /* Capital */
    table th:nth-child(5), table td:nth-child(5) { width: 8%; }  /* Intérêts */
    table th:nth-child(6), table td:nth-child(6) { width: 8%; }  /* Assurance */
    table th:nth-child(7), table td:nth-child(7) { width: 9%; }  /* Annuité */
    table th:nth-child(8), table td:nth-child(8) { width: 9%; }  /* Amortissements */
    table th:nth-child(9), table td:nth-child(9) { width: 9%; }  /* Résultat fiscal */
    table th:nth-child(10), table td:nth-child(10) { width: 8%; } /* Impôts */
    table th:nth-child(11), table td:nth-child(11) { width: 9%; } /* Trésorerie annuel */
    table th:nth-child(12), table td:nth-child(12) { width: 9%; } /* Trésorerie cumulée */
}

@media print {
    .no-print { display: none !important; }
    body { font-size: 10px; }
    table { font-size: 8px; }
    .max-w-full { max-width: 100%; }
}
</style>
{% endblock %}
