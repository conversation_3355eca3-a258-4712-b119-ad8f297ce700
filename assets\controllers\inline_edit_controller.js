import { Controller } from "@hotwired/stimulus"

export default class extends Controller {
    static targets = ["field"]
    static values = { 
        projectId: Number,
        field: String,
        type: String,
        suffix: String,
        url: String
    }

    connect() {
        this.originalValue = this.fieldTarget.textContent.trim()
        this.isEditing = false
    }

    // Start editing when clicked
    edit() {
        if (this.isEditing) return
        
        this.isEditing = true
        this.createInput()
    }

    // Create input field for editing
    createInput() {
        // Get current value without suffix
        let currentValue = this.originalValue
        if (this.suffixValue) {
            currentValue = currentValue.replace(this.suffixValue, '').trim()
        }
        if (currentValue === 'Non défini') {
            currentValue = ''
        }
        
        // Clean up number formatting for editing
        if (this.typeValue === 'number') {
            currentValue = currentValue.replace(/\s/g, '').replace(',', '.')
        }

        // Create input element
        const input = document.createElement('input')
        input.type = this.typeValue || 'text'
        input.value = currentValue
        input.className = 'w-full px-2 py-1 border border-gray-300 rounded focus:outline-none focus:ring-2 focus:ring-indigo-500'
        
        if (this.typeValue === 'number') {
            input.step = '0.01'
        }

        // Replace content with input
        this.fieldTarget.innerHTML = ''
        this.fieldTarget.appendChild(input)
        
        // Focus and select
        input.focus()
        input.select()

        // Event listeners
        input.addEventListener('blur', () => this.save(input))
        input.addEventListener('keydown', (e) => this.handleKeydown(e, input))
    }

    // Handle keyboard events
    handleKeydown(event, input) {
        if (event.key === 'Enter') {
            event.preventDefault()
            this.save(input)
        } else if (event.key === 'Escape') {
            event.preventDefault()
            this.cancel()
        }
    }

    // Save the edited value
    async save(input) {
        if (!this.isEditing) return
        
        const newValue = input.value.trim()
        
        try {
            const response = await fetch(this.urlValue || `/projet/${this.projectIdValue}/edit`, {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/x-www-form-urlencoded',
                    'X-Requested-With': 'XMLHttpRequest'
                },
                body: `field=${this.fieldValue}&value=${encodeURIComponent(newValue)}`
            })

            const data = await response.json()
            
            if (data.success) {
                this.updateDisplay(newValue)
                this.showNotification(data.message || 'Mis à jour avec succès', 'success')
            } else {
                this.cancel()
                this.showNotification(data.message || 'Erreur lors de la mise à jour', 'error')
            }
        } catch (error) {
            console.error('Error:', error)
            this.cancel()
            this.showNotification('Une erreur est survenue', 'error')
        }
        
        this.isEditing = false
    }

    // Cancel editing
    cancel() {
        this.fieldTarget.textContent = this.originalValue
        this.isEditing = false
    }

    // Update display with new value
    updateDisplay(newValue) {
        let displayValue = newValue
        
        if (displayValue && this.typeValue === 'number') {
            // Format number for display
            const numValue = parseFloat(newValue)
            if (!isNaN(numValue)) {
                displayValue = numValue.toLocaleString('fr-FR', {
                    minimumFractionDigits: 2,
                    maximumFractionDigits: 2
                })
            }
        }
        
        // Add suffix if provided
        if (displayValue && this.suffixValue) {
            displayValue += this.suffixValue
        }
        
        // Update display
        this.fieldTarget.textContent = displayValue || 'Non défini'
        this.originalValue = this.fieldTarget.textContent
    }

    // Show notification
    showNotification(message, type = 'info') {
        // Create notification element
        const notification = document.createElement('div')
        notification.className = `fixed top-4 right-4 z-50 p-4 rounded-md shadow-lg transition-all duration-300 ${
            type === 'success' ? 'bg-green-500 text-white' : 
            type === 'error' ? 'bg-red-500 text-white' : 
            'bg-blue-500 text-white'
        }`
        notification.textContent = message

        document.body.appendChild(notification)

        // Auto remove after 3 seconds
        setTimeout(() => {
            notification.style.opacity = '0'
            setTimeout(() => {
                if (notification.parentNode) {
                    notification.parentNode.removeChild(notification)
                }
            }, 300)
        }, 3000)

        // Allow manual close
        notification.addEventListener('click', () => {
            notification.style.opacity = '0'
            setTimeout(() => {
                if (notification.parentNode) {
                    notification.parentNode.removeChild(notification)
                }
            }, 300)
        })
    }
}
