<?php
namespace App\Entity;

use App\Repository\ChargeRepository;
use Doctrine\ORM\Mapping as ORM;

#[ORM\Entity(repositoryClass: ChargeRepository::class)]
class Charge
{
    public const TYPE_INITIAL = 'initial';
    public const TYPE_RECURRING = 'recurring';

    public const FREQUENCY_MONTHLY = 'monthly';
    public const FREQUENCY_QUARTERLY = 'quarterly';
    public const FREQUENCY_YEARLY = 'yearly';
    public const FREQUENCY_WEEKLY = 'weekly';

    #[ORM\Id]
    #[ORM\GeneratedValue]
    #[ORM\Column(type: "integer")]
    private ?int $id = null;

    #[ORM\ManyToOne(targetEntity: Project::class, inversedBy: "charges")]
    #[ORM\JoinColumn(nullable: false)]
    private ?Project $project = null;

    #[ORM\Column(type: "string", length: 255)]
    private string $label;

    #[ORM\Column(type: "decimal", precision: 10, scale: 2)]
    private string $amount;

    #[ORM\Column(type: "string", length: 20)]
    private string $type;

    #[ORM\Column(type: "boolean")]
    private bool $isRecursive;

    #[ORM\Column(type: "date")]
    private \DateTimeInterface $startDate;

    #[ORM\Column(type: "string", length: 20, nullable: true)]
    private ?string $frequency = null;

    #[ORM\Column(type: "text", nullable: true)]
    private ?string $description = null;

    public function getId(): ?int
    {
        return $this->id;
    }

    public function getProject(): ?Project
    {
        return $this->project;
    }

    public function setProject(Project $project): self
    {
        $this->project = $project;
        return $this;
    }

    public function getLabel(): string
    {
        return $this->label;
    }

    public function setLabel(string $label): self
    {
        $this->label = $label;
        return $this;
    }

    public function getAmount(): string
    {
        return $this->amount;
    }

    public function setAmount(string $amount): self
    {
        $this->amount = $amount;
        return $this;
    }

    public function getType(): string
    {
        return $this->type;
    }

    public function setType(string $type): self
    {
        $this->type = $type;
        return $this;
    }

    public function isRecursive(): bool
    {
        return $this->isRecursive;
    }

    public function setIsRecursive(bool $isRecursive): self
    {
        $this->isRecursive = $isRecursive;
        return $this;
    }

    public function getStartDate(): \DateTimeInterface
    {
        return $this->startDate;
    }

    public function setStartDate(\DateTimeInterface $startDate): self
    {
        $this->startDate = $startDate;
        return $this;
    }

    public function getFrequency(): ?string
    {
        return $this->frequency;
    }

    public function setFrequency(?string $frequency): self
    {
        $this->frequency = $frequency;
        return $this;
    }

    public function getDescription(): ?string
    {
        return $this->description;
    }

    public function setDescription(?string $description): self
    {
        $this->description = $description;
        return $this;
    }

    public static function getTypeChoices(): array
    {
        return [
            'Initial (One-time)' => self::TYPE_INITIAL,
            'Recurring' => self::TYPE_RECURRING,
        ];
    }

    public static function getFrequencyChoices(): array
    {
        return [
            'Weekly' => self::FREQUENCY_WEEKLY,
            'Monthly' => self::FREQUENCY_MONTHLY,
            'Quarterly' => self::FREQUENCY_QUARTERLY,
            'Yearly' => self::FREQUENCY_YEARLY,
        ];
    }

    public function isInitial(): bool
    {
        return $this->type === self::TYPE_INITIAL;
    }

    public function isRecurring(): bool
    {
        return $this->type === self::TYPE_RECURRING;
    }
}
