{% extends 'base.html.twig' %}

{% block title %}Inscription - OptiImmo{% endblock %}

{% block body %}
<div class="min-h-screen bg-gray-100 flex flex-col justify-center py-12 sm:px-6 lg:px-8">
    <div class="sm:mx-auto sm:w-full sm:max-w-md">
        <h2 class="mt-6 text-center text-3xl font-extrabold text-gray-900">
            Créer un compte
        </h2>
        <p class="mt-2 text-center text-sm text-gray-600">
            Ou 
            <a href="{{ path('app_login') }}" class="font-medium text-blue-600 hover:text-blue-500">
                connectez-vous à votre compte existant
            </a>
        </p>
    </div>

    <div class="mt-8 sm:mx-auto sm:w-full sm:max-w-md">
        <div class="bg-white py-8 px-4 shadow sm:rounded-lg sm:px-10">
            {{ form_start(registrationForm, {'attr': {'class': 'space-y-6'}}) }}
                <div>
                    {{ form_row(registrationForm.email) }}
                </div>

                <div class="grid grid-cols-2 gap-4">
                    <div>
                        {{ form_row(registrationForm.prenom) }}
                    </div>
                    <div>
                        {{ form_row(registrationForm.nom) }}
                    </div>
                </div>

                <div>
                    {{ form_row(registrationForm.plainPassword.first) }}
                </div>

                <div>
                    {{ form_row(registrationForm.plainPassword.second) }}
                </div>

                <div class="flex items-center">
                    {{ form_widget(registrationForm.agreeTerms) }}
                    {{ form_label(registrationForm.agreeTerms) }}
                </div>

                <div>
                    <button type="submit" class="w-full flex justify-center py-2 px-4 border border-transparent rounded-md shadow-sm text-sm font-medium text-white bg-blue-600 hover:bg-blue-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500">
                        S'inscrire
                    </button>
                </div>
            {{ form_end(registrationForm) }}
        </div>
    </div>
</div>
{% endblock %}
