import { Controller } from "@hotwired/stimulus"

export default class extends Controller {
    static targets = ["modal", "form", "title"]
    static values = { 
        type: String,
        projectId: Number 
    }

    connect() {
        // Bind escape key handler
        this.handleEscape = this.handleEscape.bind(this)
    }

    // Open modal
    open() {
        this.modalTarget.classList.remove('hidden')
        document.addEventListener('keydown', this.handleEscape)
        
        // Focus first input
        const firstInput = this.modalTarget.querySelector('input, select, textarea')
        if (firstInput) {
            setTimeout(() => firstInput.focus(), 100)
        }
        
        // Set default date if date input exists
        const dateInput = this.modalTarget.querySelector('input[type="date"]')
        if (dateInput && !dateInput.value) {
            dateInput.value = new Date().toISOString().split('T')[0]
        }
    }

    // Close modal
    close() {
        this.modalTarget.classList.add('hidden')
        document.removeEventListener('keydown', this.handleEscape)
        
        if (this.hasFormTarget) {
            this.formTarget.reset()
        }
    }

    // Handle backdrop click
    handleBackdrop(event) {
        if (event.target === this.modalTarget) {
            this.close()
        }
    }

    // Handle escape key
    handleEscape(event) {
        if (event.key === 'Escape') {
            this.close()
        }
    }

    // Configure modal for specific type
    configure(event) {
        const type = event.params.type || this.typeValue
        
        if (this.hasTitleTarget) {
            switch (type) {
                case 'loan':
                    this.titleTarget.textContent = 'Ajouter un prêt'
                    break
                case 'initial':
                    this.titleTarget.textContent = 'Ajouter une charge initiale'
                    this.configureChargeForm('initial')
                    break
                case 'recurring':
                    this.titleTarget.textContent = 'Ajouter une charge récurrente'
                    this.configureChargeForm('recurring')
                    break
                default:
                    this.titleTarget.textContent = 'Ajouter un élément'
            }
        }
        
        this.open()
    }

    // Configure charge form based on type
    configureChargeForm(type) {
        const typeInput = this.modalTarget.querySelector('input[name="type"]')
        const frequencyField = this.modalTarget.querySelector('#frequencyField')
        
        if (typeInput) {
            typeInput.value = type
        }
        
        if (frequencyField) {
            if (type === 'recurring') {
                frequencyField.classList.remove('hidden')
                const frequencySelect = frequencyField.querySelector('select')
                if (frequencySelect) {
                    frequencySelect.required = true
                }
            } else {
                frequencyField.classList.add('hidden')
                const frequencySelect = frequencyField.querySelector('select')
                if (frequencySelect) {
                    frequencySelect.required = false
                    frequencySelect.value = ''
                }
            }
        }
    }

    // Submit form
    async submit(event) {
        event.preventDefault()
        
        const formData = new FormData(this.formTarget)
        const submitButton = this.formTarget.querySelector('button[type="submit"]')
        
        // Disable submit button
        if (submitButton) {
            submitButton.disabled = true
            submitButton.textContent = 'Ajout en cours...'
        }
        
        try {
            const response = await fetch(this.formTarget.action, {
                method: 'POST',
                body: formData,
                headers: {
                    'X-Requested-With': 'XMLHttpRequest'
                }
            })

            const data = await response.json()
            
            if (data.success) {
                this.close()
                this.showNotification(data.message || 'Ajouté avec succès', 'success')
                
                // Reload page to show changes
                setTimeout(() => {
                    window.location.reload()
                }, 500)
            } else {
                this.showNotification(data.message || 'Une erreur est survenue', 'error')
            }
        } catch (error) {
            console.error('Error:', error)
            this.showNotification('Une erreur est survenue lors de l\'ajout', 'error')
        } finally {
            // Re-enable submit button
            if (submitButton) {
                submitButton.disabled = false
                submitButton.textContent = 'Ajouter'
            }
        }
    }

    // Show notification
    showNotification(message, type = 'info') {
        const notification = document.createElement('div')
        notification.className = `fixed top-4 right-4 z-50 p-4 rounded-md shadow-lg transition-all duration-300 ${
            type === 'success' ? 'bg-green-500 text-white' : 
            type === 'error' ? 'bg-red-500 text-white' : 
            'bg-blue-500 text-white'
        }`
        notification.textContent = message

        document.body.appendChild(notification)

        setTimeout(() => {
            notification.style.opacity = '0'
            setTimeout(() => {
                if (notification.parentNode) {
                    notification.parentNode.removeChild(notification)
                }
            }, 300)
        }, 4000)

        notification.addEventListener('click', () => {
            notification.style.opacity = '0'
            setTimeout(() => {
                if (notification.parentNode) {
                    notification.parentNode.removeChild(notification)
                }
            }, 300)
        })
    }
}
