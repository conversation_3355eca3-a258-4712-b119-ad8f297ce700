<?php

namespace App\Service;

use App\Entity\Loan;

class LoanCalculatorService
{
    /**
     * Calculate monthly payment for a loan
     */
    public function calculateMonthlyPayment(Loan $loan): float
    {
        $principal = (float) $loan->getAmount();
        $monthlyInterestRate = (float) $loan->getInterestRate() / 100 / 12;
        $monthlyInsuranceRate = (float) $loan->getInsuranceRate() / 100 / 12;
        $numberOfPayments = $loan->getDurationYears() * 12;

        if ($monthlyInterestRate == 0) {
            $monthlyPrincipalAndInterest = $principal / $numberOfPayments;
        } else {
            $monthlyPrincipalAndInterest = $principal * 
                ($monthlyInterestRate * pow(1 + $monthlyInterestRate, $numberOfPayments)) / 
                (pow(1 + $monthlyInterestRate, $numberOfPayments) - 1);
        }

        $monthlyInsurance = $principal * $monthlyInsuranceRate;
        
        return $monthlyPrincipalAndInterest + $monthlyInsurance;
    }

    /**
     * Calculate total cost of the loan
     */
    public function calculateTotalCost(Loan $loan): float
    {
        $monthlyPayment = $this->calculateMonthlyPayment($loan);
        $numberOfPayments = $loan->getDurationYears() * 12;
        
        return $monthlyPayment * $numberOfPayments;
    }

    /**
     * Calculate total interest paid
     */
    public function calculateTotalInterest(Loan $loan): float
    {
        $totalCost = $this->calculateTotalCost($loan);
        $principal = (float) $loan->getAmount();
        
        return $totalCost - $principal;
    }

    /**
     * Calculate remaining balance at a given month
     */
    public function calculateRemainingBalance(Loan $loan, int $monthNumber): float
    {
        if ($monthNumber <= 0) {
            return (float) $loan->getAmount();
        }

        $principal = (float) $loan->getAmount();
        $monthlyInterestRate = (float) $loan->getInterestRate() / 100 / 12;
        $numberOfPayments = $loan->getDurationYears() * 12;
        $monthlyPayment = $this->calculateMonthlyPayment($loan);

        if ($monthNumber >= $numberOfPayments) {
            return 0.0;
        }

        if ($monthlyInterestRate == 0) {
            return $principal - ($monthlyPayment * $monthNumber);
        }

        $remainingBalance = $principal * 
            (pow(1 + $monthlyInterestRate, $numberOfPayments) - pow(1 + $monthlyInterestRate, $monthNumber)) /
            (pow(1 + $monthlyInterestRate, $numberOfPayments) - 1);

        return max(0, $remainingBalance);
    }

    /**
     * Generate amortization schedule with deferred period handling
     */
    public function generateAmortizationSchedule(Loan $loan, int $maxMonths = null): array
    {
        $schedule = [];
        $principal = (float) $loan->getAmount();
        $monthlyInterestRate = (float) $loan->getInterestRate() / 100 / 12;
        $monthlyInsuranceRate = (float) $loan->getInsuranceRate() / 100 / 12;
        $deferredMonths = $loan->getDeferredMonths();
        $totalMonths = ($loan->getDurationYears() * 12) + $deferredMonths;
        $monthlyInsurance = $principal * $monthlyInsuranceRate;

        $remainingBalance = $principal;
        $maxMonths = $maxMonths ?: $totalMonths;

        // Calculate monthly payment for non-deferred period
        $regularMonthlyPayment = $this->calculateRegularMonthlyPayment($loan);

        for ($month = 1; $month <= min($maxMonths, $totalMonths); $month++) {
            $interestPayment = $remainingBalance * $monthlyInterestRate;

            if ($month <= $deferredMonths) {
                // Deferred period: only pay interest and insurance
                $principalPayment = 0;
                $totalPayment = $interestPayment + $monthlyInsurance;
                $isDeferredPeriod = true;
            } else {
                // Regular period: pay principal, interest and insurance
                $principalPayment = $regularMonthlyPayment - $interestPayment - $monthlyInsurance;
                $totalPayment = $regularMonthlyPayment;
                $isDeferredPeriod = false;

                // Adjust for final payment
                if ($principalPayment > $remainingBalance) {
                    $principalPayment = $remainingBalance;
                    $totalPayment = $principalPayment + $interestPayment + $monthlyInsurance;
                }

                $remainingBalance -= $principalPayment;
            }

            $schedule[] = [
                'month' => $month,
                'payment' => $totalPayment,
                'principal' => $principalPayment,
                'interest' => $interestPayment,
                'insurance' => $monthlyInsurance,
                'balance' => max(0, $remainingBalance),
                'isDeferred' => $isDeferredPeriod
            ];

            if (!$isDeferredPeriod && $remainingBalance <= 0) {
                break;
            }
        }

        return $schedule;
    }

    /**
     * Calculate regular monthly payment (excluding deferred period)
     */
    private function calculateRegularMonthlyPayment(Loan $loan): float
    {
        $principal = (float) $loan->getAmount();
        $monthlyInterestRate = (float) $loan->getInterestRate() / 100 / 12;
        $monthlyInsuranceRate = (float) $loan->getInsuranceRate() / 100 / 12;
        $numberOfPayments = $loan->getDurationYears() * 12; // Excluding deferred months

        if ($monthlyInterestRate == 0) {
            $monthlyPrincipalAndInterest = $principal / $numberOfPayments;
        } else {
            $monthlyPrincipalAndInterest = $principal *
                ($monthlyInterestRate * pow(1 + $monthlyInterestRate, $numberOfPayments)) /
                (pow(1 + $monthlyInterestRate, $numberOfPayments) - 1);
        }

        $monthlyInsurance = $principal * $monthlyInsuranceRate;

        return $monthlyPrincipalAndInterest + $monthlyInsurance;
    }

    /**
     * Calculate yearly payment (annuity)
     */
    public function calculateYearlyPayment(Loan $loan): float
    {
        return $this->calculateMonthlyPayment($loan) * 12;
    }

    /**
     * Generate combined amortization schedule for multiple loans
     */
    public function generateCombinedAmortizationSchedule(array $loans): array
    {
        if (empty($loans)) {
            return [];
        }

        // Generate individual schedules with real dates
        $loanSchedules = [];
        $allDates = [];

        foreach ($loans as $loan) {
            $schedule = $this->generateAmortizationScheduleWithDates($loan);
            $loanSchedules[$loan->getId()] = [
                'loan' => $loan,
                'schedule' => $schedule
            ];

            // Collect all unique dates
            foreach ($schedule as $row) {
                $dateKey = $row['date']->format('Y-m');
                if (!in_array($dateKey, $allDates)) {
                    $allDates[] = $dateKey;
                }
            }
        }

        // Sort dates
        sort($allDates);

        // Create combined schedule
        $combinedSchedule = [];

        foreach ($allDates as $dateKey) {
            $date = \DateTime::createFromFormat('Y-m', $dateKey);
            $date->modify('first day of this month');

            $monthData = [
                'date' => $date,
                'dateFormatted' => $date->format('M Y'),
                'loans' => [],
                'totals' => [
                    'payment' => 0,
                    'principal' => 0,
                    'interest' => 0,
                    'insurance' => 0,
                    'balance' => 0
                ]
            ];

            // Add data for each loan for this month
            foreach ($loanSchedules as $loanId => $loanData) {
                $loan = $loanData['loan'];
                $schedule = $loanData['schedule'];

                // Find the row for this date
                $loanMonthData = null;
                foreach ($schedule as $row) {
                    if ($row['date']->format('Y-m') === $dateKey) {
                        $loanMonthData = $row;
                        break;
                    }
                }

                if ($loanMonthData) {
                    $monthData['loans'][$loanId] = $loanMonthData;
                    $monthData['totals']['payment'] += $loanMonthData['payment'];
                    $monthData['totals']['principal'] += $loanMonthData['principal'];
                    $monthData['totals']['interest'] += $loanMonthData['interest'];
                    $monthData['totals']['insurance'] += $loanMonthData['insurance'];
                    $monthData['totals']['balance'] += $loanMonthData['balance'];
                } else {
                    // No payment for this loan this month
                    $monthData['loans'][$loanId] = null;
                }
            }

            $combinedSchedule[] = $monthData;
        }

        return $combinedSchedule;
    }

    /**
     * Generate amortization schedule with real dates
     */
    private function generateAmortizationScheduleWithDates(Loan $loan): array
    {
        $schedule = [];
        $principal = (float) $loan->getAmount();
        $monthlyInterestRate = (float) $loan->getInterestRate() / 100 / 12;
        $monthlyInsuranceRate = (float) $loan->getInsuranceRate() / 100 / 12;
        $deferredMonths = $loan->getDeferredMonths();
        $totalMonths = ($loan->getDurationYears() * 12) + $deferredMonths;
        $monthlyInsurance = $principal * $monthlyInsuranceRate;

        $remainingBalance = $principal;
        $currentDate = clone $loan->getStartDate();

        // Calculate monthly payment for non-deferred period
        $regularMonthlyPayment = $this->calculateRegularMonthlyPayment($loan);

        for ($month = 1; $month <= $totalMonths; $month++) {
            $interestPayment = $remainingBalance * $monthlyInterestRate;

            if ($month <= $deferredMonths) {
                // Deferred period: only pay interest and insurance
                $principalPayment = 0;
                $totalPayment = $interestPayment + $monthlyInsurance;
                $isDeferredPeriod = true;
            } else {
                // Regular period: pay principal, interest and insurance
                $principalPayment = $regularMonthlyPayment - $interestPayment - $monthlyInsurance;
                $totalPayment = $regularMonthlyPayment;
                $isDeferredPeriod = false;

                // Adjust for final payment
                if ($principalPayment > $remainingBalance) {
                    $principalPayment = $remainingBalance;
                    $totalPayment = $principalPayment + $interestPayment + $monthlyInsurance;
                }

                $remainingBalance -= $principalPayment;
            }

            $schedule[] = [
                'month' => $month,
                'date' => clone $currentDate,
                'payment' => $totalPayment,
                'principal' => $principalPayment,
                'interest' => $interestPayment,
                'insurance' => $monthlyInsurance,
                'balance' => max(0, $remainingBalance),
                'isDeferred' => $isDeferredPeriod,
                'loanId' => $loan->getId()
            ];

            // Move to next month
            $currentDate->modify('+1 month');

            if (!$isDeferredPeriod && $remainingBalance <= 0) {
                break;
            }
        }

        return $schedule;
    }

    /**
     * Calculate loan statistics
     */
    public function calculateLoanStatistics(Loan $loan): array
    {
        $monthlyPayment = $this->calculateMonthlyPayment($loan);
        $totalCost = $this->calculateTotalCost($loan);
        $totalInterest = $this->calculateTotalInterest($loan);
        $principal = (float) $loan->getAmount();
        
        return [
            'principal' => $principal,
            'monthlyPayment' => $monthlyPayment,
            'totalCost' => $totalCost,
            'totalInterest' => $totalInterest,
            'interestRate' => (float) $loan->getInterestRate(),
            'insuranceRate' => (float) $loan->getInsuranceRate(),
            'durationYears' => $loan->getDurationYears(),
            'durationMonths' => $loan->getDurationYears() * 12,
            'effectiveRate' => ($totalInterest / $principal) * 100,
            'monthlyInsurance' => $principal * ((float) $loan->getInsuranceRate() / 100 / 12)
        ];
    }

    /**
     * Calculate multiple loans summary
     */
    public function calculateMultipleLoansStatistics(array $loans): array
    {
        $totalPrincipal = 0;
        $totalMonthlyPayment = 0;
        $totalCost = 0;
        $totalInterest = 0;
        $loansData = [];

        foreach ($loans as $loan) {
            $stats = $this->calculateLoanStatistics($loan);
            $loansData[] = $stats;
            
            $totalPrincipal += $stats['principal'];
            $totalMonthlyPayment += $stats['monthlyPayment'];
            $totalCost += $stats['totalCost'];
            $totalInterest += $stats['totalInterest'];
        }

        return [
            'loans' => $loansData,
            'summary' => [
                'totalPrincipal' => $totalPrincipal,
                'totalMonthlyPayment' => $totalMonthlyPayment,
                'totalCost' => $totalCost,
                'totalInterest' => $totalInterest,
                'averageRate' => $totalPrincipal > 0 ? ($totalInterest / $totalPrincipal) * 100 : 0,
                'count' => count($loans)
            ]
        ];
    }
}
