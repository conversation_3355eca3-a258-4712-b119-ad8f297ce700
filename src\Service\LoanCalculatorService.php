<?php

namespace App\Service;

use App\Entity\Loan;

class LoanCalculatorService
{
    /**
     * Calculate monthly payment for a loan
     */
    public function calculateMonthlyPayment(Loan $loan): float
    {
        $principal = (float) $loan->getAmount();
        $monthlyInterestRate = (float) $loan->getInterestRate() / 100 / 12;
        $monthlyInsuranceRate = (float) $loan->getInsuranceRate() / 100 / 12;
        $numberOfPayments = $loan->getDurationYears() * 12;

        if ($monthlyInterestRate == 0) {
            $monthlyPrincipalAndInterest = $principal / $numberOfPayments;
        } else {
            $monthlyPrincipalAndInterest = $principal * 
                ($monthlyInterestRate * pow(1 + $monthlyInterestRate, $numberOfPayments)) / 
                (pow(1 + $monthlyInterestRate, $numberOfPayments) - 1);
        }

        $monthlyInsurance = $principal * $monthlyInsuranceRate;
        
        return $monthlyPrincipalAndInterest + $monthlyInsurance;
    }

    /**
     * Calculate total cost of the loan
     */
    public function calculateTotalCost(Loan $loan): float
    {
        $monthlyPayment = $this->calculateMonthlyPayment($loan);
        $numberOfPayments = $loan->getDurationYears() * 12;
        
        return $monthlyPayment * $numberOfPayments;
    }

    /**
     * Calculate total interest paid
     */
    public function calculateTotalInterest(Loan $loan): float
    {
        $totalCost = $this->calculateTotalCost($loan);
        $principal = (float) $loan->getAmount();
        
        return $totalCost - $principal;
    }

    /**
     * Calculate remaining balance at a given month
     */
    public function calculateRemainingBalance(Loan $loan, int $monthNumber): float
    {
        if ($monthNumber <= 0) {
            return (float) $loan->getAmount();
        }

        $principal = (float) $loan->getAmount();
        $monthlyInterestRate = (float) $loan->getInterestRate() / 100 / 12;
        $numberOfPayments = $loan->getDurationYears() * 12;
        $monthlyPayment = $this->calculateMonthlyPayment($loan);

        if ($monthNumber >= $numberOfPayments) {
            return 0.0;
        }

        if ($monthlyInterestRate == 0) {
            return $principal - ($monthlyPayment * $monthNumber);
        }

        $remainingBalance = $principal * 
            (pow(1 + $monthlyInterestRate, $numberOfPayments) - pow(1 + $monthlyInterestRate, $monthNumber)) /
            (pow(1 + $monthlyInterestRate, $numberOfPayments) - 1);

        return max(0, $remainingBalance);
    }

    /**
     * Generate amortization schedule
     */
    public function generateAmortizationSchedule(Loan $loan, int $maxMonths = null): array
    {
        $schedule = [];
        $principal = (float) $loan->getAmount();
        $monthlyInterestRate = (float) $loan->getInterestRate() / 100 / 12;
        $monthlyInsuranceRate = (float) $loan->getInsuranceRate() / 100 / 12;
        $numberOfPayments = $loan->getDurationYears() * 12;
        $monthlyPayment = $this->calculateMonthlyPayment($loan);
        $monthlyInsurance = $principal * $monthlyInsuranceRate;
        
        $remainingBalance = $principal;
        $maxMonths = $maxMonths ?: $numberOfPayments;
        
        for ($month = 1; $month <= min($maxMonths, $numberOfPayments); $month++) {
            $interestPayment = $remainingBalance * $monthlyInterestRate;
            $principalPayment = $monthlyPayment - $interestPayment - $monthlyInsurance;
            
            // Adjust for final payment
            if ($principalPayment > $remainingBalance) {
                $principalPayment = $remainingBalance;
                $monthlyPayment = $principalPayment + $interestPayment + $monthlyInsurance;
            }
            
            $remainingBalance -= $principalPayment;
            
            $schedule[] = [
                'month' => $month,
                'payment' => $monthlyPayment,
                'principal' => $principalPayment,
                'interest' => $interestPayment,
                'insurance' => $monthlyInsurance,
                'balance' => max(0, $remainingBalance)
            ];
            
            if ($remainingBalance <= 0) {
                break;
            }
        }
        
        return $schedule;
    }

    /**
     * Calculate loan statistics
     */
    public function calculateLoanStatistics(Loan $loan): array
    {
        $monthlyPayment = $this->calculateMonthlyPayment($loan);
        $totalCost = $this->calculateTotalCost($loan);
        $totalInterest = $this->calculateTotalInterest($loan);
        $principal = (float) $loan->getAmount();
        
        return [
            'principal' => $principal,
            'monthlyPayment' => $monthlyPayment,
            'totalCost' => $totalCost,
            'totalInterest' => $totalInterest,
            'interestRate' => (float) $loan->getInterestRate(),
            'insuranceRate' => (float) $loan->getInsuranceRate(),
            'durationYears' => $loan->getDurationYears(),
            'durationMonths' => $loan->getDurationYears() * 12,
            'effectiveRate' => ($totalInterest / $principal) * 100,
            'monthlyInsurance' => $principal * ((float) $loan->getInsuranceRate() / 100 / 12)
        ];
    }

    /**
     * Calculate multiple loans summary
     */
    public function calculateMultipleLoansStatistics(array $loans): array
    {
        $totalPrincipal = 0;
        $totalMonthlyPayment = 0;
        $totalCost = 0;
        $totalInterest = 0;
        $loansData = [];

        foreach ($loans as $loan) {
            $stats = $this->calculateLoanStatistics($loan);
            $loansData[] = $stats;
            
            $totalPrincipal += $stats['principal'];
            $totalMonthlyPayment += $stats['monthlyPayment'];
            $totalCost += $stats['totalCost'];
            $totalInterest += $stats['totalInterest'];
        }

        return [
            'loans' => $loansData,
            'summary' => [
                'totalPrincipal' => $totalPrincipal,
                'totalMonthlyPayment' => $totalMonthlyPayment,
                'totalCost' => $totalCost,
                'totalInterest' => $totalInterest,
                'averageRate' => $totalPrincipal > 0 ? ($totalInterest / $totalPrincipal) * 100 : 0,
                'count' => count($loans)
            ]
        ];
    }
}
