<?php

namespace App\Form;

use App\Entity\Loan;
use Symfony\Component\Form\AbstractType;
use Symfony\Component\Form\Extension\Core\Type\NumberType;
use Symfony\Component\Form\Extension\Core\Type\IntegerType;
use Symfony\Component\Form\Extension\Core\Type\DateType;
use Symfony\Component\Form\FormBuilderInterface;
use Symfony\Component\OptionsResolver\OptionsResolver;
use Symfony\Component\Validator\Constraints as Assert;

class LoanType extends AbstractType
{
    public function buildForm(FormBuilderInterface $builder, array $options): void
    {
        $builder
            ->add('amount', NumberType::class, [
                'label' => 'Montant du prêt (€)',
                'attr' => [
                    'class' => 'w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-indigo-500',
                    'placeholder' => '100000.00',
                    'step' => '0.01'
                ],
                'constraints' => [
                    new Assert\NotBlank(['message' => 'Le montant du prêt est requis']),
                    new Assert\Positive(['message' => 'Le montant du prêt doit être positif'])
                ]
            ])
            ->add('interestRate', NumberType::class, [
                'label' => 'Taux d\'intérêt (%)',
                'attr' => [
                    'class' => 'w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-indigo-500',
                    'placeholder' => '2.50',
                    'step' => '0.01',
                    'min' => '0',
                    'max' => '20'
                ],
                'constraints' => [
                    new Assert\NotBlank(['message' => 'Le taux d\'intérêt est requis']),
                    new Assert\Range(['min' => 0, 'max' => 20, 'notInRangeMessage' => 'Le taux d\'intérêt doit être entre 0 et 20%'])
                ]
            ])
            ->add('insuranceRate', NumberType::class, [
                'label' => 'Taux d\'assurance (%)',
                'attr' => [
                    'class' => 'w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-indigo-500',
                    'placeholder' => '0.36',
                    'step' => '0.01',
                    'min' => '0',
                    'max' => '5'
                ],
                'constraints' => [
                    new Assert\NotBlank(['message' => 'Le taux d\'assurance est requis']),
                    new Assert\Range(['min' => 0, 'max' => 5, 'notInRangeMessage' => 'Le taux d\'assurance doit être entre 0 et 5%'])
                ]
            ])
            ->add('durationYears', IntegerType::class, [
                'label' => 'Durée (années)',
                'attr' => [
                    'class' => 'w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-indigo-500',
                    'placeholder' => '20',
                    'min' => '1',
                    'max' => '50'
                ],
                'constraints' => [
                    new Assert\NotBlank(['message' => 'La durée du prêt est requise']),
                    new Assert\Range(['min' => 1, 'max' => 50, 'notInRangeMessage' => 'La durée doit être entre 1 et 50 ans'])
                ]
            ])
            ->add('startDate', DateType::class, [
                'label' => 'Date de début',
                'widget' => 'single_text',
                'attr' => [
                    'class' => 'w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-indigo-500'
                ],
                'constraints' => [
                    new Assert\NotBlank(['message' => 'La date de début est requise'])
                ]
            ])
            ->add('deferredMonths', IntegerType::class, [
                'label' => 'Mois de différé',
                'required' => false,
                'attr' => [
                    'class' => 'w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-indigo-500',
                    'placeholder' => '0',
                    'min' => '0',
                    'max' => '24'
                ],
                'constraints' => [
                    new Assert\Range(['min' => 0, 'max' => 24, 'notInRangeMessage' => 'Le différé doit être entre 0 et 24 mois'])
                ]
            ]);
    }

    public function configureOptions(OptionsResolver $resolver): void
    {
        $resolver->setDefaults([
            'data_class' => Loan::class,
        ]);
    }
}
